!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).i18next=t()}(this,(function(){"use strict";const e=e=>"string"==typeof e,t=()=>{let e,t;const s=new Promise(((s,i)=>{e=s,t=i}));return s.resolve=e,s.reject=t,s},s=e=>null==e?"":""+e,i=/###/g,o=e=>e&&e.indexOf("###")>-1?e.replace(i,"."):e,n=t=>!t||e(t),a=(t,s,i)=>{const a=e(s)?s.split("."):s;let r=0;for(;r<a.length-1;){if(n(t))return{};const e=o(a[r]);!t[e]&&i&&(t[e]=new i),t=Object.prototype.hasOwnProperty.call(t,e)?t[e]:{},++r}return n(t)?{}:{obj:t,k:o(a[r])}},r=(e,t,s)=>{const{obj:i,k:o}=a(e,t,Object);if(void 0!==i||1===t.length)return void(i[o]=s);let n=t[t.length-1],r=t.slice(0,t.length-1),l=a(e,r,Object);for(;void 0===l.obj&&r.length;)n=`${r[r.length-1]}.${n}`,r=r.slice(0,r.length-1),l=a(e,r,Object),l?.obj&&void 0!==l.obj[`${l.k}.${n}`]&&(l.obj=void 0);l.obj[`${l.k}.${n}`]=s},l=(e,t)=>{const{obj:s,k:i}=a(e,t);if(s&&Object.prototype.hasOwnProperty.call(s,i))return s[i]},h=(t,s,i)=>{for(const o in s)"__proto__"!==o&&"constructor"!==o&&(o in t?e(t[o])||t[o]instanceof String||e(s[o])||s[o]instanceof String?i&&(t[o]=s[o]):h(t[o],s[o],i):t[o]=s[o]);return t},p=e=>e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var u={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const c=t=>e(t)?t.replace(/[&<>"'\/]/g,(e=>u[e])):t;const g=[" ",",","?","!",";"],d=new class{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const t=this.regExpMap.get(e);if(void 0!==t)return t;const s=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,s),this.regExpQueue.push(e),s}}(20),f=(e,t,s=".")=>{if(!e)return;if(e[t]){if(!Object.prototype.hasOwnProperty.call(e,t))return;return e[t]}const i=t.split(s);let o=e;for(let e=0;e<i.length;){if(!o||"object"!=typeof o)return;let t,n="";for(let a=e;a<i.length;++a)if(a!==e&&(n+=s),n+=i[a],t=o[n],void 0!==t){if(["string","number","boolean"].indexOf(typeof t)>-1&&a<i.length-1)continue;e+=a-e+1;break}o=t}return o},m=e=>e?.replace("_","-"),y={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,t){console?.[e]?.apply?.(console,t)}};class x{constructor(e,t={}){this.init(e,t)}init(e,t={}){this.prefix=t.prefix||"i18next:",this.logger=e||y,this.options=t,this.debug=t.debug}log(...e){return this.forward(e,"log","",!0)}warn(...e){return this.forward(e,"warn","",!0)}error(...e){return this.forward(e,"error","")}deprecate(...e){return this.forward(e,"warn","WARNING DEPRECATED: ",!0)}forward(t,s,i,o){return o&&!this.debug?null:(e(t[0])&&(t[0]=`${i}${this.prefix} ${t[0]}`),this.logger[s](t))}create(e){return new x(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return(e=e||this.options).prefix=e.prefix||this.prefix,new x(this.logger,e)}}var v=new x;class b{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach((e=>{this.observers[e]||(this.observers[e]=new Map);const s=this.observers[e].get(t)||0;this.observers[e].set(t,s+1)})),this}off(e,t){this.observers[e]&&(t?this.observers[e].delete(t):delete this.observers[e])}emit(e,...t){if(this.observers[e]){Array.from(this.observers[e].entries()).forEach((([e,s])=>{for(let i=0;i<s;i++)e(...t)}))}if(this.observers["*"]){Array.from(this.observers["*"].entries()).forEach((([s,i])=>{for(let o=0;o<i;o++)s.apply(s,[e,...t])}))}}}class k extends b{constructor(e,t={ns:["translation"],defaultNS:"translation"}){super(),this.data=e||{},this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),void 0===this.options.ignoreJSONStructure&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(t,s,i,o={}){const n=void 0!==o.keySeparator?o.keySeparator:this.options.keySeparator,a=void 0!==o.ignoreJSONStructure?o.ignoreJSONStructure:this.options.ignoreJSONStructure;let r;t.indexOf(".")>-1?r=t.split("."):(r=[t,s],i&&(Array.isArray(i)?r.push(...i):e(i)&&n?r.push(...i.split(n)):r.push(i)));const h=l(this.data,r);return!h&&!s&&!i&&t.indexOf(".")>-1&&(t=r[0],s=r[1],i=r.slice(2).join(".")),!h&&a&&e(i)?f(this.data?.[t]?.[s],i,n):h}addResource(e,t,s,i,o={silent:!1}){const n=void 0!==o.keySeparator?o.keySeparator:this.options.keySeparator;let a=[e,t];s&&(a=a.concat(n?s.split(n):s)),e.indexOf(".")>-1&&(a=e.split("."),i=t,t=a[1]),this.addNamespaces(t),r(this.data,a,i),o.silent||this.emit("added",e,t,s,i)}addResources(t,s,i,o={silent:!1}){for(const o in i)(e(i[o])||Array.isArray(i[o]))&&this.addResource(t,s,o,i[o],{silent:!0});o.silent||this.emit("added",t,s,i)}addResourceBundle(e,t,s,i,o,n={silent:!1,skipCopy:!1}){let a=[e,t];e.indexOf(".")>-1&&(a=e.split("."),i=s,s=t,t=a[1]),this.addNamespaces(t);let p=l(this.data,a)||{};n.skipCopy||(s=JSON.parse(JSON.stringify(s))),i?h(p,s,o):p={...p,...s},r(this.data,a,p),n.silent||this.emit("added",e,t,s)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return void 0!==this.getResource(e,t)}getResourceBundle(e,t){return t||(t=this.options.defaultNS),this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find((e=>t[e]&&Object.keys(t[e]).length>0))}toJSON(){return this.data}}var S={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,t,s,i,o){return e.forEach((e=>{t=this.processors[e]?.process(t,s,i,o)??t})),t}};const O={},L=t=>!e(t)&&"boolean"!=typeof t&&"number"!=typeof t;class w extends b{constructor(e,t={}){var s,i;super(),s=e,i=this,["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"].forEach((e=>{s[e]&&(i[e]=s[e])})),this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),this.logger=v.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e,t={interpolation:{}}){const s={...t};if(null==e)return!1;const i=this.resolve(e,s);return void 0!==i?.res}extractFromKey(t,s){let i=void 0!==s.nsSeparator?s.nsSeparator:this.options.nsSeparator;void 0===i&&(i=":");const o=void 0!==s.keySeparator?s.keySeparator:this.options.keySeparator;let n=s.ns||this.options.defaultNS||[];const a=i&&t.indexOf(i)>-1,r=!(this.options.userDefinedKeySeparator||s.keySeparator||this.options.userDefinedNsSeparator||s.nsSeparator||((e,t,s)=>{t=t||"",s=s||"";const i=g.filter((e=>t.indexOf(e)<0&&s.indexOf(e)<0));if(0===i.length)return!0;const o=d.getRegExp(`(${i.map((e=>"?"===e?"\\?":e)).join("|")})`);let n=!o.test(e);if(!n){const t=e.indexOf(s);t>0&&!o.test(e.substring(0,t))&&(n=!0)}return n})(t,i,o));if(a&&!r){const s=t.match(this.interpolator.nestingRegexp);if(s&&s.length>0)return{key:t,namespaces:e(n)?[n]:n};const a=t.split(i);(i!==o||i===o&&this.options.ns.indexOf(a[0])>-1)&&(n=a.shift()),t=a.join(o)}return{key:t,namespaces:e(n)?[n]:n}}translate(t,s,i){let o="object"==typeof s?{...s}:s;if("object"!=typeof o&&this.options.overloadTranslationOptionHandler&&(o=this.options.overloadTranslationOptionHandler(arguments)),"object"==typeof options&&(o={...o}),o||(o={}),null==t)return"";Array.isArray(t)||(t=[String(t)]);const n=void 0!==o.returnDetails?o.returnDetails:this.options.returnDetails,a=void 0!==o.keySeparator?o.keySeparator:this.options.keySeparator,{key:r,namespaces:l}=this.extractFromKey(t[t.length-1],o),h=l[l.length-1];let p=void 0!==o.nsSeparator?o.nsSeparator:this.options.nsSeparator;void 0===p&&(p=":");const u=o.lng||this.language,c=o.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if("cimode"===u?.toLowerCase())return c?n?{res:`${h}${p}${r}`,usedKey:r,exactUsedKey:r,usedLng:u,usedNS:h,usedParams:this.getUsedParamsDetails(o)}:`${h}${p}${r}`:n?{res:r,usedKey:r,exactUsedKey:r,usedLng:u,usedNS:h,usedParams:this.getUsedParamsDetails(o)}:r;const g=this.resolve(t,o);let d=g?.res;const f=g?.usedKey||r,m=g?.exactUsedKey||r,y=void 0!==o.joinArrays?o.joinArrays:this.options.joinArrays,x=!this.i18nFormat||this.i18nFormat.handleAsObject,v=void 0!==o.count&&!e(o.count),b=w.hasDefaultValue(o),k=v?this.pluralResolver.getSuffix(u,o.count,o):"",S=o.ordinal&&v?this.pluralResolver.getSuffix(u,o.count,{ordinal:!1}):"",O=v&&!o.ordinal&&0===o.count,$=O&&o[`defaultValue${this.options.pluralSeparator}zero`]||o[`defaultValue${k}`]||o[`defaultValue${S}`]||o.defaultValue;let R=d;x&&!d&&b&&(R=$);const C=L(R),P=Object.prototype.toString.apply(R);if(!(x&&R&&C&&["[object Number]","[object Function]","[object RegExp]"].indexOf(P)<0)||e(y)&&Array.isArray(R))if(x&&e(y)&&Array.isArray(d))d=d.join(y),d&&(d=this.extendTranslation(d,t,o,i));else{let e=!1,s=!1;!this.isValidLookup(d)&&b&&(e=!0,d=$),this.isValidLookup(d)||(s=!0,d=r);const n=(o.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&s?void 0:d,l=b&&$!==d&&this.options.updateMissing;if(s||e||l){if(this.logger.log(l?"updateKey":"missingKey",u,h,r,l?$:d),a){const e=this.resolve(r,{...o,keySeparator:!1});e&&e.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let e=[];const t=this.languageUtils.getFallbackCodes(this.options.fallbackLng,o.lng||this.language);if("fallback"===this.options.saveMissingTo&&t&&t[0])for(let s=0;s<t.length;s++)e.push(t[s]);else"all"===this.options.saveMissingTo?e=this.languageUtils.toResolveHierarchy(o.lng||this.language):e.push(o.lng||this.language);const s=(e,t,s)=>{const i=b&&s!==d?s:n;this.options.missingKeyHandler?this.options.missingKeyHandler(e,h,t,i,l,o):this.backendConnector?.saveMissing&&this.backendConnector.saveMissing(e,h,t,i,l,o),this.emit("missingKey",e,h,t,d)};this.options.saveMissing&&(this.options.saveMissingPlurals&&v?e.forEach((e=>{const t=this.pluralResolver.getSuffixes(e,o);O&&o[`defaultValue${this.options.pluralSeparator}zero`]&&t.indexOf(`${this.options.pluralSeparator}zero`)<0&&t.push(`${this.options.pluralSeparator}zero`),t.forEach((t=>{s([e],r+t,o[`defaultValue${t}`]||$)}))})):s(e,r,$))}d=this.extendTranslation(d,t,o,g,i),s&&d===r&&this.options.appendNamespaceToMissingKey&&(d=`${h}${p}${r}`),(s||e)&&this.options.parseMissingKeyHandler&&(d=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${h}${p}${r}`:r,e?d:void 0,o))}else{if(!o.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const e=this.options.returnedObjectHandler?this.options.returnedObjectHandler(f,R,{...o,ns:l}):`key '${r} (${this.language})' returned an object instead of string.`;return n?(g.res=e,g.usedParams=this.getUsedParamsDetails(o),g):e}if(a){const e=Array.isArray(R),t=e?[]:{},s=e?m:f;for(const e in R)if(Object.prototype.hasOwnProperty.call(R,e)){const i=`${s}${a}${e}`;t[e]=b&&!d?this.translate(i,{...o,defaultValue:L($)?$[e]:void 0,joinArrays:!1,ns:l}):this.translate(i,{...o,joinArrays:!1,ns:l}),t[e]===i&&(t[e]=R[e])}d=t}}return n?(g.res=d,g.usedParams=this.getUsedParamsDetails(o),g):d}extendTranslation(t,s,i,o,n){if(this.i18nFormat?.parse)t=this.i18nFormat.parse(t,{...this.options.interpolation.defaultVariables,...i},i.lng||this.language||o.usedLng,o.usedNS,o.usedKey,{resolved:o});else if(!i.skipInterpolation){i.interpolation&&this.interpolator.init({...i,interpolation:{...this.options.interpolation,...i.interpolation}});const a=e(t)&&(void 0!==i?.interpolation?.skipOnVariables?i.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let r;if(a){const e=t.match(this.interpolator.nestingRegexp);r=e&&e.length}let l=i.replace&&!e(i.replace)?i.replace:i;if(this.options.interpolation.defaultVariables&&(l={...this.options.interpolation.defaultVariables,...l}),t=this.interpolator.interpolate(t,l,i.lng||this.language||o.usedLng,i),a){const e=t.match(this.interpolator.nestingRegexp);r<(e&&e.length)&&(i.nest=!1)}!i.lng&&o&&o.res&&(i.lng=this.language||o.usedLng),!1!==i.nest&&(t=this.interpolator.nest(t,((...e)=>n?.[0]!==e[0]||i.context?this.translate(...e,s):(this.logger.warn(`It seems you are nesting recursively key: ${e[0]} in key: ${s[0]}`),null)),i)),i.interpolation&&this.interpolator.reset()}const a=i.postProcess||this.options.postProcess,r=e(a)?[a]:a;return null!=t&&r?.length&&!1!==i.applyPostProcessor&&(t=S.handle(r,t,s,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...o,usedParams:this.getUsedParamsDetails(i)},...i}:i,this)),t}resolve(t,s={}){let i,o,n,a,r;return e(t)&&(t=[t]),t.forEach((t=>{if(this.isValidLookup(i))return;const l=this.extractFromKey(t,s),h=l.key;o=h;let p=l.namespaces;this.options.fallbackNS&&(p=p.concat(this.options.fallbackNS));const u=void 0!==s.count&&!e(s.count),c=u&&!s.ordinal&&0===s.count,g=void 0!==s.context&&(e(s.context)||"number"==typeof s.context)&&""!==s.context,d=s.lngs?s.lngs:this.languageUtils.toResolveHierarchy(s.lng||this.language,s.fallbackLng);p.forEach((e=>{this.isValidLookup(i)||(r=e,O[`${d[0]}-${e}`]||!this.utils?.hasLoadedNamespace||this.utils?.hasLoadedNamespace(r)||(O[`${d[0]}-${e}`]=!0,this.logger.warn(`key "${o}" for languages "${d.join(", ")}" won't get resolved as namespace "${r}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),d.forEach((t=>{if(this.isValidLookup(i))return;a=t;const o=[h];if(this.i18nFormat?.addLookupKeys)this.i18nFormat.addLookupKeys(o,h,t,e,s);else{let e;u&&(e=this.pluralResolver.getSuffix(t,s.count,s));const i=`${this.options.pluralSeparator}zero`,n=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(u&&(o.push(h+e),s.ordinal&&0===e.indexOf(n)&&o.push(h+e.replace(n,this.options.pluralSeparator)),c&&o.push(h+i)),g){const t=`${h}${this.options.contextSeparator}${s.context}`;o.push(t),u&&(o.push(t+e),s.ordinal&&0===e.indexOf(n)&&o.push(t+e.replace(n,this.options.pluralSeparator)),c&&o.push(t+i))}}let r;for(;r=o.pop();)this.isValidLookup(i)||(n=r,i=this.getResource(t,e,r,s))})))}))})),{res:i,usedKey:o,exactUsedKey:n,usedLng:a,usedNS:r}}isValidLookup(e){return!(void 0===e||!this.options.returnNull&&null===e||!this.options.returnEmptyString&&""===e)}getResource(e,t,s,i={}){return this.i18nFormat?.getResource?this.i18nFormat.getResource(e,t,s,i):this.resourceStore.getResource(e,t,s,i)}getUsedParamsDetails(t={}){const s=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],i=t.replace&&!e(t.replace);let o=i?t.replace:t;if(i&&void 0!==t.count&&(o.count=t.count),this.options.interpolation.defaultVariables&&(o={...this.options.interpolation.defaultVariables,...o}),!i){o={...o};for(const e of s)delete o[e]}return o}static hasDefaultValue(e){const t="defaultValue";for(const s in e)if(Object.prototype.hasOwnProperty.call(e,s)&&t===s.substring(0,12)&&void 0!==e[s])return!0;return!1}}class ${constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=v.create("languageUtils")}getScriptPartFromCode(e){if(!(e=m(e))||e.indexOf("-")<0)return null;const t=e.split("-");return 2===t.length?null:(t.pop(),"x"===t[t.length-1].toLowerCase()?null:this.formatLanguageCode(t.join("-")))}getLanguagePartFromCode(e){if(!(e=m(e))||e.indexOf("-")<0)return e;const t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(t){if(e(t)&&t.indexOf("-")>-1){let e;try{e=Intl.getCanonicalLocales(t)[0]}catch(e){}return e&&this.options.lowerCaseLng&&(e=e.toLowerCase()),e||(this.options.lowerCaseLng?t.toLowerCase():t)}return this.options.cleanCode||this.options.lowerCaseLng?t.toLowerCase():t}isSupportedCode(e){return("languageOnly"===this.options.load||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let t;return e.forEach((e=>{if(t)return;const s=this.formatLanguageCode(e);this.options.supportedLngs&&!this.isSupportedCode(s)||(t=s)})),!t&&this.options.supportedLngs&&e.forEach((e=>{if(t)return;const s=this.getScriptPartFromCode(e);if(this.isSupportedCode(s))return t=s;const i=this.getLanguagePartFromCode(e);if(this.isSupportedCode(i))return t=i;t=this.options.supportedLngs.find((e=>e===i?e:e.indexOf("-")<0&&i.indexOf("-")<0?void 0:e.indexOf("-")>0&&i.indexOf("-")<0&&e.substring(0,e.indexOf("-"))===i||0===e.indexOf(i)&&i.length>1?e:void 0))})),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t}getFallbackCodes(t,s){if(!t)return[];if("function"==typeof t&&(t=t(s)),e(t)&&(t=[t]),Array.isArray(t))return t;if(!s)return t.default||[];let i=t[s];return i||(i=t[this.getScriptPartFromCode(s)]),i||(i=t[this.formatLanguageCode(s)]),i||(i=t[this.getLanguagePartFromCode(s)]),i||(i=t.default),i||[]}toResolveHierarchy(t,s){const i=this.getFallbackCodes((!1===s?[]:s)||this.options.fallbackLng||[],t),o=[],n=e=>{e&&(this.isSupportedCode(e)?o.push(e):this.logger.warn(`rejecting language code not found in supportedLngs: ${e}`))};return e(t)&&(t.indexOf("-")>-1||t.indexOf("_")>-1)?("languageOnly"!==this.options.load&&n(this.formatLanguageCode(t)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&n(this.getScriptPartFromCode(t)),"currentOnly"!==this.options.load&&n(this.getLanguagePartFromCode(t))):e(t)&&n(this.formatLanguageCode(t)),i.forEach((e=>{o.indexOf(e)<0&&n(this.formatLanguageCode(e))})),o}}const R={zero:0,one:1,two:2,few:3,many:4,other:5},C={select:e=>1===e?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class P{constructor(e,t={}){this.languageUtils=e,this.options=t,this.logger=v.create("pluralResolver"),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e,t={}){const s=m("dev"===e?"en":e),i=t.ordinal?"ordinal":"cardinal",o=JSON.stringify({cleanedCode:s,type:i});if(o in this.pluralRulesCache)return this.pluralRulesCache[o];let n;try{n=new Intl.PluralRules(s,{type:i})}catch(s){if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),C;if(!e.match(/-|_/))return C;const i=this.languageUtils.getLanguagePartFromCode(e);n=this.getRule(i,t)}return this.pluralRulesCache[o]=n,n}needsPlural(e,t={}){let s=this.getRule(e,t);return s||(s=this.getRule("dev",t)),s?.resolvedOptions().pluralCategories.length>1}getPluralFormsOfKey(e,t,s={}){return this.getSuffixes(e,s).map((e=>`${t}${e}`))}getSuffixes(e,t={}){let s=this.getRule(e,t);return s||(s=this.getRule("dev",t)),s?s.resolvedOptions().pluralCategories.sort(((e,t)=>R[e]-R[t])).map((e=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${e}`)):[]}getSuffix(e,t,s={}){const i=this.getRule(e,s);return i?`${this.options.prepend}${s.ordinal?`ordinal${this.options.prepend}`:""}${i.select(t)}`:(this.logger.warn(`no plural rule found for: ${e}`),this.getSuffix("dev",t,s))}}const N=(t,s,i,o=".",n=!0)=>{let a=((e,t,s)=>{const i=l(e,s);return void 0!==i?i:l(t,s)})(t,s,i);return!a&&n&&e(i)&&(a=f(t,i,o),void 0===a&&(a=f(s,i,o))),a},j=e=>e.replace(/\$/g,"$$$$");class E{constructor(e={}){this.logger=v.create("interpolator"),this.options=e,this.format=e?.interpolation?.format||(e=>e),this.init(e)}init(e={}){e.interpolation||(e.interpolation={escapeValue:!0});const{escape:t,escapeValue:s,useRawValueToEscape:i,prefix:o,prefixEscaped:n,suffix:a,suffixEscaped:r,formatSeparator:l,unescapeSuffix:h,unescapePrefix:u,nestingPrefix:g,nestingPrefixEscaped:d,nestingSuffix:f,nestingSuffixEscaped:m,nestingOptionsSeparator:y,maxReplaces:x,alwaysFormat:v}=e.interpolation;this.escape=void 0!==t?t:c,this.escapeValue=void 0===s||s,this.useRawValueToEscape=void 0!==i&&i,this.prefix=o?p(o):n||"{{",this.suffix=a?p(a):r||"}}",this.formatSeparator=l||",",this.unescapePrefix=h?"":u||"-",this.unescapeSuffix=this.unescapePrefix?"":h||"",this.nestingPrefix=g?p(g):d||p("$t("),this.nestingSuffix=f?p(f):m||p(")"),this.nestingOptionsSeparator=y||",",this.maxReplaces=x||1e3,this.alwaysFormat=void 0!==v&&v,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(e,t)=>e?.source===t?(e.lastIndex=0,e):new RegExp(t,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(t,i,o,n){let a,r,l;const h=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},p=e=>{if(e.indexOf(this.formatSeparator)<0){const t=N(i,h,e,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(t,void 0,o,{...n,...i,interpolationkey:e}):t}const t=e.split(this.formatSeparator),s=t.shift().trim(),a=t.join(this.formatSeparator).trim();return this.format(N(i,h,s,this.options.keySeparator,this.options.ignoreJSONStructure),a,o,{...n,...i,interpolationkey:s})};this.resetRegExp();const u=n?.missingInterpolationHandler||this.options.missingInterpolationHandler,c=void 0!==n?.interpolation?.skipOnVariables?n.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:e=>j(e)},{regex:this.regexp,safeValue:e=>this.escapeValue?j(this.escape(e)):j(e)}].forEach((i=>{for(l=0;a=i.regex.exec(t);){const o=a[1].trim();if(r=p(o),void 0===r)if("function"==typeof u){const s=u(t,a,n);r=e(s)?s:""}else if(n&&Object.prototype.hasOwnProperty.call(n,o))r="";else{if(c){r=a[0];continue}this.logger.warn(`missed to pass in variable ${o} for interpolating ${t}`),r=""}else e(r)||this.useRawValueToEscape||(r=s(r));const h=i.safeValue(r);if(t=t.replace(a[0],h),c?(i.regex.lastIndex+=r.length,i.regex.lastIndex-=a[0].length):i.regex.lastIndex=0,l++,l>=this.maxReplaces)break}})),t}nest(t,i,o={}){let n,a,r;const l=(e,t)=>{const s=this.nestingOptionsSeparator;if(e.indexOf(s)<0)return e;const i=e.split(new RegExp(`${s}[ ]*{`));let o=`{${i[1]}`;e=i[0],o=this.interpolate(o,r);const n=o.match(/'/g),a=o.match(/"/g);((n?.length??0)%2==0&&!a||a.length%2!=0)&&(o=o.replace(/'/g,'"'));try{r=JSON.parse(o),t&&(r={...t,...r})}catch(t){return this.logger.warn(`failed parsing options string in nesting for key ${e}`,t),`${e}${s}${o}`}return r.defaultValue&&r.defaultValue.indexOf(this.prefix)>-1&&delete r.defaultValue,e};for(;n=this.nestingRegexp.exec(t);){let h=[];r={...o},r=r.replace&&!e(r.replace)?r.replace:r,r.applyPostProcessor=!1,delete r.defaultValue;let p=!1;if(-1!==n[0].indexOf(this.formatSeparator)&&!/{.*}/.test(n[1])){const e=n[1].split(this.formatSeparator).map((e=>e.trim()));n[1]=e.shift(),h=e,p=!0}if(a=i(l.call(this,n[1].trim(),r),r),a&&n[0]===t&&!e(a))return a;e(a)||(a=s(a)),a||(this.logger.warn(`missed to resolve ${n[1]} for nesting ${t}`),a=""),p&&(a=h.reduce(((e,t)=>this.format(e,t,o.lng,{...o,interpolationkey:n[1].trim()})),a.trim())),t=t.replace(n[0],a),this.regexp.lastIndex=0}return t}}const F=e=>{const t={};return(s,i,o)=>{let n=o;o&&o.interpolationkey&&o.formatParams&&o.formatParams[o.interpolationkey]&&o[o.interpolationkey]&&(n={...n,[o.interpolationkey]:void 0});const a=i+JSON.stringify(n);let r=t[a];return r||(r=e(m(i),o),t[a]=r),r(s)}},V=e=>(t,s,i)=>e(m(s),i)(t);class D{constructor(e={}){this.logger=v.create("formatter"),this.options=e,this.init(e)}init(e,t={interpolation:{}}){this.formatSeparator=t.interpolation.formatSeparator||",";const s=t.cacheInBuiltFormats?F:V;this.formats={number:s(((e,t)=>{const s=new Intl.NumberFormat(e,{...t});return e=>s.format(e)})),currency:s(((e,t)=>{const s=new Intl.NumberFormat(e,{...t,style:"currency"});return e=>s.format(e)})),datetime:s(((e,t)=>{const s=new Intl.DateTimeFormat(e,{...t});return e=>s.format(e)})),relativetime:s(((e,t)=>{const s=new Intl.RelativeTimeFormat(e,{...t});return e=>s.format(e,t.range||"day")})),list:s(((e,t)=>{const s=new Intl.ListFormat(e,{...t});return e=>s.format(e)}))}}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=F(t)}format(e,t,s,i={}){const o=t.split(this.formatSeparator);if(o.length>1&&o[0].indexOf("(")>1&&o[0].indexOf(")")<0&&o.find((e=>e.indexOf(")")>-1))){const e=o.findIndex((e=>e.indexOf(")")>-1));o[0]=[o[0],...o.splice(1,e)].join(this.formatSeparator)}return o.reduce(((e,t)=>{const{formatName:o,formatOptions:n}=(e=>{let t=e.toLowerCase().trim();const s={};if(e.indexOf("(")>-1){const i=e.split("(");t=i[0].toLowerCase().trim();const o=i[1].substring(0,i[1].length-1);"currency"===t&&o.indexOf(":")<0?s.currency||(s.currency=o.trim()):"relativetime"===t&&o.indexOf(":")<0?s.range||(s.range=o.trim()):o.split(";").forEach((e=>{if(e){const[t,...i]=e.split(":"),o=i.join(":").trim().replace(/^'+|'+$/g,""),n=t.trim();s[n]||(s[n]=o),"false"===o&&(s[n]=!1),"true"===o&&(s[n]=!0),isNaN(o)||(s[n]=parseInt(o,10))}}))}return{formatName:t,formatOptions:s}})(t);if(this.formats[o]){let t=e;try{const a=i?.formatParams?.[i.interpolationkey]||{},r=a.locale||a.lng||i.locale||i.lng||s;t=this.formats[o](e,r,{...n,...i,...a})}catch(e){this.logger.warn(e)}return t}return this.logger.warn(`there was no format function for ${o}`),e}),e)}}class I extends b{constructor(e,t,s,i={}){super(),this.backend=e,this.store=t,this.services=s,this.languageUtils=s.languageUtils,this.options=i,this.logger=v.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=i.maxParallelReads||10,this.readingCalls=0,this.maxRetries=i.maxRetries>=0?i.maxRetries:5,this.retryTimeout=i.retryTimeout>=1?i.retryTimeout:350,this.state={},this.queue=[],this.backend?.init?.(s,i.backend,i)}queueLoad(e,t,s,i){const o={},n={},a={},r={};return e.forEach((e=>{let i=!0;t.forEach((t=>{const a=`${e}|${t}`;!s.reload&&this.store.hasResourceBundle(e,t)?this.state[a]=2:this.state[a]<0||(1===this.state[a]?void 0===n[a]&&(n[a]=!0):(this.state[a]=1,i=!1,void 0===n[a]&&(n[a]=!0),void 0===o[a]&&(o[a]=!0),void 0===r[t]&&(r[t]=!0)))})),i||(a[e]=!0)})),(Object.keys(o).length||Object.keys(n).length)&&this.queue.push({pending:n,pendingCount:Object.keys(n).length,loaded:{},errors:[],callback:i}),{toLoad:Object.keys(o),pending:Object.keys(n),toLoadLanguages:Object.keys(a),toLoadNamespaces:Object.keys(r)}}loaded(e,t,s){const i=e.split("|"),o=i[0],n=i[1];t&&this.emit("failedLoading",o,n,t),!t&&s&&this.store.addResourceBundle(o,n,s,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&s&&(this.state[e]=0);const r={};this.queue.forEach((s=>{((e,t,s)=>{const{obj:i,k:o}=a(e,t,Object);i[o]=i[o]||[],i[o].push(s)})(s.loaded,[o],n),((e,t)=>{void 0!==e.pending[t]&&(delete e.pending[t],e.pendingCount--)})(s,e),t&&s.errors.push(t),0!==s.pendingCount||s.done||(Object.keys(s.loaded).forEach((e=>{r[e]||(r[e]={});const t=s.loaded[e];t.length&&t.forEach((t=>{void 0===r[e][t]&&(r[e][t]=!0)}))})),s.done=!0,s.errors.length?s.callback(s.errors):s.callback())})),this.emit("loaded",r),this.queue=this.queue.filter((e=>!e.done))}read(e,t,s,i=0,o=this.retryTimeout,n){if(!e.length)return n(null,{});if(this.readingCalls>=this.maxParallelReads)return void this.waitingReads.push({lng:e,ns:t,fcName:s,tried:i,wait:o,callback:n});this.readingCalls++;const a=(a,r)=>{if(this.readingCalls--,this.waitingReads.length>0){const e=this.waitingReads.shift();this.read(e.lng,e.ns,e.fcName,e.tried,e.wait,e.callback)}a&&r&&i<this.maxRetries?setTimeout((()=>{this.read.call(this,e,t,s,i+1,2*o,n)}),o):n(a,r)},r=this.backend[s].bind(this.backend);if(2!==r.length)return r(e,t,a);try{const s=r(e,t);s&&"function"==typeof s.then?s.then((e=>a(null,e))).catch(a):a(null,s)}catch(e){a(e)}}prepareLoading(t,s,i={},o){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),o&&o();e(t)&&(t=this.languageUtils.toResolveHierarchy(t)),e(s)&&(s=[s]);const n=this.queueLoad(t,s,i,o);if(!n.toLoad.length)return n.pending.length||o(),null;n.toLoad.forEach((e=>{this.loadOne(e)}))}load(e,t,s){this.prepareLoading(e,t,{},s)}reload(e,t,s){this.prepareLoading(e,t,{reload:!0},s)}loadOne(e,t=""){const s=e.split("|"),i=s[0],o=s[1];this.read(i,o,"read",void 0,void 0,((s,n)=>{s&&this.logger.warn(`${t}loading namespace ${o} for language ${i} failed`,s),!s&&n&&this.logger.log(`${t}loaded namespace ${o} for language ${i}`,n),this.loaded(e,s,n)}))}saveMissing(e,t,s,i,o,n={},a=()=>{}){if(!this.services?.utils?.hasLoadedNamespace||this.services?.utils?.hasLoadedNamespace(t)){if(null!=s&&""!==s){if(this.backend?.create){const r={...n,isUpdate:o},l=this.backend.create.bind(this.backend);if(l.length<6)try{let o;o=5===l.length?l(e,t,s,i,r):l(e,t,s,i),o&&"function"==typeof o.then?o.then((e=>a(null,e))).catch(a):a(null,o)}catch(e){a(e)}else l(e,t,s,i,a,r)}e&&e[0]&&this.store.addResource(e[0],t,s,i)}}else this.logger.warn(`did not save key "${s}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")}}const T=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:t=>{let s={};if("object"==typeof t[1]&&(s=t[1]),e(t[1])&&(s.defaultValue=t[1]),e(t[2])&&(s.tDescription=t[2]),"object"==typeof t[2]||"object"==typeof t[3]){const e=t[3]||t[2];Object.keys(e).forEach((t=>{s[t]=e[t]}))}return s},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),K=t=>(e(t.ns)&&(t.ns=[t.ns]),e(t.fallbackLng)&&(t.fallbackLng=[t.fallbackLng]),e(t.fallbackNS)&&(t.fallbackNS=[t.fallbackNS]),t.supportedLngs?.indexOf?.("cimode")<0&&(t.supportedLngs=t.supportedLngs.concat(["cimode"])),"boolean"==typeof t.initImmediate&&(t.initAsync=t.initImmediate),t),U=()=>{};class A extends b{constructor(e={},t){var s;if(super(),this.options=K(e),this.services={},this.logger=v,this.modules={external:[]},s=this,Object.getOwnPropertyNames(Object.getPrototypeOf(s)).forEach((e=>{"function"==typeof s[e]&&(s[e]=s[e].bind(s))})),t&&!this.isInitialized&&!e.isClone){if(!this.options.initAsync)return this.init(e,t),this;setTimeout((()=>{this.init(e,t)}),0)}}init(s={},i){this.isInitializing=!0,"function"==typeof s&&(i=s,s={}),null==s.defaultNS&&s.ns&&(e(s.ns)?s.defaultNS=s.ns:s.ns.indexOf("translation")<0&&(s.defaultNS=s.ns[0]));const o=T();this.options={...o,...this.options,...K(s)},this.options.interpolation={...o.interpolation,...this.options.interpolation},void 0!==s.keySeparator&&(this.options.userDefinedKeySeparator=s.keySeparator),void 0!==s.nsSeparator&&(this.options.userDefinedNsSeparator=s.nsSeparator);const n=e=>e?"function"==typeof e?new e:e:null;if(!this.options.isClone){let e;this.modules.logger?v.init(n(this.modules.logger),this.options):v.init(null,this.options),e=this.modules.formatter?this.modules.formatter:D;const t=new $(this.options);this.store=new k(this.options.resources,this.options);const s=this.services;s.logger=v,s.resourceStore=this.store,s.languageUtils=t,s.pluralResolver=new P(t,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),!e||this.options.interpolation.format&&this.options.interpolation.format!==o.interpolation.format||(s.formatter=n(e),s.formatter.init(s,this.options),this.options.interpolation.format=s.formatter.format.bind(s.formatter)),s.interpolator=new E(this.options),s.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},s.backendConnector=new I(n(this.modules.backend),s.resourceStore,s,this.options),s.backendConnector.on("*",((e,...t)=>{this.emit(e,...t)})),this.modules.languageDetector&&(s.languageDetector=n(this.modules.languageDetector),s.languageDetector.init&&s.languageDetector.init(s,this.options.detection,this.options)),this.modules.i18nFormat&&(s.i18nFormat=n(this.modules.i18nFormat),s.i18nFormat.init&&s.i18nFormat.init(this)),this.translator=new w(this.services,this.options),this.translator.on("*",((e,...t)=>{this.emit(e,...t)})),this.modules.external.forEach((e=>{e.init&&e.init(this)}))}if(this.format=this.options.interpolation.format,i||(i=U),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const e=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);e.length>0&&"dev"!==e[0]&&(this.options.lng=e[0])}this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined");["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach((e=>{this[e]=(...t)=>this.store[e](...t)}));["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach((e=>{this[e]=(...t)=>(this.store[e](...t),this)}));const a=t(),r=()=>{const e=(e,t)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),a.resolve(t),i(e,t)};if(this.languages&&!this.isInitialized)return e(null,this.t.bind(this));this.changeLanguage(this.options.lng,e)};return this.options.resources||!this.options.initAsync?r():setTimeout(r,0),a}loadResources(t,s=U){let i=s;const o=e(t)?t:this.language;if("function"==typeof t&&(i=t),!this.options.resources||this.options.partialBundledLanguages){if("cimode"===o?.toLowerCase()&&(!this.options.preload||0===this.options.preload.length))return i();const e=[],t=t=>{if(!t)return;if("cimode"===t)return;this.services.languageUtils.toResolveHierarchy(t).forEach((t=>{"cimode"!==t&&e.indexOf(t)<0&&e.push(t)}))};if(o)t(o);else{this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach((e=>t(e)))}this.options.preload?.forEach?.((e=>t(e))),this.services.backendConnector.load(e,this.options.ns,(e=>{e||this.resolvedLanguage||!this.language||this.setResolvedLanguage(this.language),i(e)}))}else i(null)}reloadResources(e,s,i){const o=t();return"function"==typeof e&&(i=e,e=void 0),"function"==typeof s&&(i=s,s=void 0),e||(e=this.languages),s||(s=this.options.ns),i||(i=U),this.services.backendConnector.reload(e,s,(e=>{o.resolve(),i(e)})),o}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return"backend"===e.type&&(this.modules.backend=e),("logger"===e.type||e.log&&e.warn&&e.error)&&(this.modules.logger=e),"languageDetector"===e.type&&(this.modules.languageDetector=e),"i18nFormat"===e.type&&(this.modules.i18nFormat=e),"postProcessor"===e.type&&S.addPostProcessor(e),"formatter"===e.type&&(this.modules.formatter=e),"3rdParty"===e.type&&this.modules.external.push(e),this}setResolvedLanguage(e){if(e&&this.languages&&!(["cimode","dev"].indexOf(e)>-1)){for(let e=0;e<this.languages.length;e++){const t=this.languages[e];if(!(["cimode","dev"].indexOf(t)>-1)&&this.store.hasLanguageSomeTranslations(t)){this.resolvedLanguage=t;break}}!this.resolvedLanguage&&this.languages.indexOf(e)<0&&this.store.hasLanguageSomeTranslations(e)&&(this.resolvedLanguage=e,this.languages.unshift(e))}}changeLanguage(s,i){this.isLanguageChangingTo=s;const o=t();this.emit("languageChanging",s);const n=e=>{this.language=e,this.languages=this.services.languageUtils.toResolveHierarchy(e),this.resolvedLanguage=void 0,this.setResolvedLanguage(e)},a=(e,t)=>{t?this.isLanguageChangingTo===s&&(n(t),this.translator.changeLanguage(t),this.isLanguageChangingTo=void 0,this.emit("languageChanged",t),this.logger.log("languageChanged",t)):this.isLanguageChangingTo=void 0,o.resolve(((...e)=>this.t(...e))),i&&i(e,((...e)=>this.t(...e)))},r=t=>{s||t||!this.services.languageDetector||(t=[]);const i=e(t)?t:t&&t[0],o=this.store.hasLanguageSomeTranslations(i)?i:this.services.languageUtils.getBestMatchFromCodes(e(t)?[t]:t);o&&(this.language||n(o),this.translator.language||this.translator.changeLanguage(o),this.services.languageDetector?.cacheUserLanguage?.(o)),this.loadResources(o,(e=>{a(e,o)}))};return s||!this.services.languageDetector||this.services.languageDetector.async?!s&&this.services.languageDetector&&this.services.languageDetector.async?0===this.services.languageDetector.detect.length?this.services.languageDetector.detect().then(r):this.services.languageDetector.detect(r):r(s):r(this.services.languageDetector.detect()),o}getFixedT(t,s,i){const o=(e,t,...s)=>{let n;n="object"!=typeof t?this.options.overloadTranslationOptionHandler([e,t].concat(s)):{...t},n.lng=n.lng||o.lng,n.lngs=n.lngs||o.lngs,n.ns=n.ns||o.ns,""!==n.keyPrefix&&(n.keyPrefix=n.keyPrefix||i||o.keyPrefix);const a=this.options.keySeparator||".";let r;return r=n.keyPrefix&&Array.isArray(e)?e.map((e=>`${n.keyPrefix}${a}${e}`)):n.keyPrefix?`${n.keyPrefix}${a}${e}`:e,this.t(r,n)};return e(t)?o.lng=t:o.lngs=t,o.ns=s,o.keyPrefix=i,o}t(...e){return this.translator?.translate(...e)}exists(...e){return this.translator?.exists(...e)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e,t={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const s=t.lng||this.resolvedLanguage||this.languages[0],i=!!this.options&&this.options.fallbackLng,o=this.languages[this.languages.length-1];if("cimode"===s.toLowerCase())return!0;const n=(e,t)=>{const s=this.services.backendConnector.state[`${e}|${t}`];return-1===s||0===s||2===s};if(t.precheck){const e=t.precheck(this,n);if(void 0!==e)return e}return!!this.hasResourceBundle(s,e)||(!(this.services.backendConnector.backend&&(!this.options.resources||this.options.partialBundledLanguages))||!(!n(s,e)||i&&!n(o,e)))}loadNamespaces(s,i){const o=t();return this.options.ns?(e(s)&&(s=[s]),s.forEach((e=>{this.options.ns.indexOf(e)<0&&this.options.ns.push(e)})),this.loadResources((e=>{o.resolve(),i&&i(e)})),o):(i&&i(),Promise.resolve())}loadLanguages(s,i){const o=t();e(s)&&(s=[s]);const n=this.options.preload||[],a=s.filter((e=>n.indexOf(e)<0&&this.services.languageUtils.isSupportedCode(e)));return a.length?(this.options.preload=n.concat(a),this.loadResources((e=>{o.resolve(),i&&i(e)})),o):(i&&i(),Promise.resolve())}dir(e){if(e||(e=this.resolvedLanguage||(this.languages?.length>0?this.languages[0]:this.language)),!e)return"rtl";const t=this.services?.languageUtils||new $(T());return["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"].indexOf(t.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(e={},t){return new A(e,t)}cloneInstance(e={},t=U){const s=e.forkResourceStore;s&&delete e.forkResourceStore;const i={...this.options,...e,isClone:!0},o=new A(i);void 0===e.debug&&void 0===e.prefix||(o.logger=o.logger.clone(e));if(["store","services","language"].forEach((e=>{o[e]=this[e]})),o.services={...this.services},o.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},s){const e=Object.keys(this.store.data).reduce(((e,t)=>(e[t]={...this.store.data[t]},e[t]=Object.keys(e[t]).reduce(((s,i)=>(s[i]={...e[t][i]},s)),e[t]),e)),{});o.store=new k(e,i),o.services.resourceStore=o.store}return o.translator=new w(o.services,i),o.translator.on("*",((e,...t)=>{o.emit(e,...t)})),o.init(i,t),o.translator.options=i,o.translator.backendConnector.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},o}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const M=A.createInstance();return M.createInstance=A.createInstance,M}));
