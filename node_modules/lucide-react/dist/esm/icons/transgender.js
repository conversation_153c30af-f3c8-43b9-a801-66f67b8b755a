/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 16v6", key: "c8a4gj" }],
  ["path", { d: "M14 20h-4", key: "m8m19d" }],
  ["path", { d: "M18 2h4v4", key: "1341mj" }],
  ["path", { d: "m2 2 7.17 7.17", key: "13q8l2" }],
  ["path", { d: "M2 5.355V2h3.357", key: "18136r" }],
  ["path", { d: "m22 2-7.17 7.17", key: "1epvy4" }],
  ["path", { d: "M8 5 5 8", key: "mgbjhz" }],
  ["circle", { cx: "12", cy: "12", r: "4", key: "4exip2" }]
];
const Transgender = createLucideIcon("transgender", __iconNode);

export { __iconNode, Transgender as default };
//# sourceMappingURL=transgender.js.map
