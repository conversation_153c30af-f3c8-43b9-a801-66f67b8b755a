import t from"void-elements";const e=/\s([^'"/\s><]+?)[\s/>]|([^\s=]+)=\s?(".*?"|'.*?')/g;function n(n){const s={type:"tag",name:"",voidElement:!1,attrs:{},children:[]},c=n.match(/<\/?([^\s]+?)[/\s>]/);if(c&&(s.name=c[1],(t[c[1]]||"/"===n.charAt(n.length-2))&&(s.voidElement=!0),s.name.startsWith("!--"))){const t=n.indexOf("--\x3e");return{type:"comment",comment:-1!==t?n.slice(4,t):""}}const r=new RegExp(e);let i=null;for(;i=r.exec(n),null!==i;)if(i[0].trim())if(i[1]){const t=i[1].trim();let e=[t,""];t.indexOf("=")>-1&&(e=t.split("=")),s.attrs[e[0]]=e[1],r.lastIndex--}else i[2]&&(s.attrs[i[2]]=i[3].trim().substring(1,i[3].length-1));return s}const s=/<[a-zA-Z0-9\-\!\/](?:"[^"]*"|'[^']*'|[^'">])*>/g,c=/^\s*$/,r=Object.create(null);function i(t,e){switch(e.type){case"text":return t+e.content;case"tag":return t+="<"+e.name+(e.attrs?function(t){const e=[];for(let n in t)e.push(n+'="'+t[n]+'"');return e.length?" "+e.join(" "):""}(e.attrs):"")+(e.voidElement?"/>":">"),e.voidElement?t:t+e.children.reduce(i,"")+"</"+e.name+">";case"comment":return t+"\x3c!--"+e.comment+"--\x3e"}}var o={parse:function(t,e){e||(e={}),e.components||(e.components=r);const i=[],o=[];let l,m=-1,u=!1;if(0!==t.indexOf("<")){var a=t.indexOf("<");i.push({type:"text",content:-1===a?t:t.substring(0,a)})}return t.replace(s,function(s,r){if(u){if(s!=="</"+l.name+">")return;u=!1}const a="/"!==s.charAt(1),f=s.startsWith("\x3c!--"),h=r+s.length,p=t.charAt(h);let d;if(f){const t=n(s);return m<0?(i.push(t),i):(d=o[m],d.children.push(t),i)}if(a&&(m++,l=n(s),"tag"===l.type&&e.components[l.name]&&(l.type="component",u=!0),l.voidElement||u||!p||"<"===p||l.children.push({type:"text",content:t.slice(h,t.indexOf("<",h))}),0===m&&i.push(l),d=o[m-1],d&&d.children.push(l),o[m]=l),(!a||l.voidElement)&&(m>-1&&(l.voidElement||l.name===s.slice(2,-1))&&(m--,l=-1===m?i:o[m]),!u&&"<"!==p&&p)){d=-1===m?i:o[m].children;const e=t.indexOf("<",h);let n=t.slice(h,-1===e?void 0:e);c.test(n)&&(n=" "),(e>-1&&m+d.length>=0||" "!==n)&&d.push({type:"text",content:n})}}),i},stringify:function(t){return t.reduce(function(t,e){return t+i("",e)},"")}};export default o;
//# sourceMappingURL=html-parse-stringify.modern.js.map
