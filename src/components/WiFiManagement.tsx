import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Wifi, RefreshCw, Lock, Unlock, AlertTriangle, X, Eye, EyeOff } from 'lucide-react';
import { type WiFiNetwork, type WiFiStatus } from '../services/api';

interface WiFiManagementProps {
  networks: WiFiNetwork[];
  status: WiFiStatus | null;
  loading: boolean;
  onScan: () => void;
  onConnect: (ssid: string, password?: string) => Promise<void>;
  onDisconnect: () => Promise<void>;
}

const WiFiManagement: React.FC<WiFiManagementProps> = ({
  networks,
  status,
  onScan,
  onConnect,
  onDisconnect
}) => {
  const { t } = useTranslation();
  const [scanning, setScanning] = useState(false);
  const [connecting, setConnecting] = useState<string | null>(null);
  const [disconnecting, setDisconnecting] = useState(false);
  const [showDisconnectConfirm, setShowDisconnectConfirm] = useState(false);
  const [passwordDialog, setPasswordDialog] = useState<{ ssid: string; show: boolean }>({
    ssid: '',
    show: false
  });
  const [password, setPassword] = useState('');
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [wpa3Blocked, setWpa3Blocked] = useState(false);

  const handleScan = async () => {
    setScanning(true);
    await onScan();
    setTimeout(() => setScanning(false), 1000);
  };

  const handleConnect = async (ssid: string, isSecure: boolean, security?: string) => {
    if (security && security.includes('WPA3')) {
      setWpa3Blocked(true);
      return;
    }
    setWpa3Blocked(false);
    if (isSecure) {
      setPasswordDialog({ ssid, show: true });
      setPassword('');
    } else {
      setConnecting(ssid);
      try {
        await onConnect(ssid);
      } finally {
        setConnecting(null);
      }
    }
  };

  const handlePasswordConnect = async () => {
    setConnecting(passwordDialog.ssid);
    setPasswordDialog({ ssid: '', show: false });
    try {
      await onConnect(passwordDialog.ssid, password);
    } finally {
      setConnecting(null);
      setPassword('');
    }
  };

  const handleDisconnectClick = () => {
    setShowDisconnectConfirm(true);
  };

  const handleDisconnectConfirm = async () => {
    setShowDisconnectConfirm(false);
    setDisconnecting(true);
    try {
      await onDisconnect();
    } finally {
      setDisconnecting(false);
    }
  };

  const handleDisconnectCancel = () => {
    setShowDisconnectConfirm(false);
  };

  const getSignalStrength = (signal: number): 'weak' | 'medium' | 'strong' => {
    if (signal >= 70) return 'strong';
    if (signal >= 50) return 'medium';
    return 'weak';
  };

  const SignalBars: React.FC<{ strength: 'weak' | 'medium' | 'strong' }> = ({ strength }) => {
    const bars = [25, 50, 75, 100];
    const getOpacity = (index: number) => {
      if (strength === 'weak') return index === 0 ? 1 : 0.3;
      if (strength === 'medium') return index <= 1 ? 1 : 0.3;
      return 1;
    };

    return (
      <div className="flex items-end gap-0.5 w-4 h-4">
        {bars.map((height, index) => (
          <div
            key={index}
            className="w-0.5 bg-blue-500 rounded-sm transition-all duration-300"
            style={{
              height: `${height}%`,
              opacity: getOpacity(index)
            }}
          />
        ))}
      </div>
    );
  };

  return (
    <div className="glass-card p-6 flex-[2]">
      <div className="flex items-center gap-3 mb-6">
        <Wifi size={24} className="text-blue-400" />
        <h2 className="text-xl font-semibold text-blue-400">
          {t('wifiManagement.title')}
        </h2>
      </div>

      {/* 当前连接状态 */}
      {status?.state === 'connected' && (
        <div className="relative bg-green-500/10 border border-green-500/30 rounded-xl p-5 mb-6 overflow-hidden">
          <div className="absolute top-0 left-0 w-1 h-full bg-green-500"></div>

          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse-dot"></div>
              <span className="text-green-400 font-semibold text-sm">
                {t('wifiManagement.connected')}
              </span>
            </div>
            <button
              className="btn-danger"
              onClick={handleDisconnectClick}
              disabled={disconnecting}
            >
              {disconnecting ? (
                <div className="loading"></div>
              ) : (
                t('wifiManagement.disconnect')
              )}
            </button>
          </div>

          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center">
              <Wifi size={24} className="text-white" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-white mb-1">{status.ssid}</h3>
              <p className="text-gray-300 text-sm mb-1">
                IP4: {status.IP4}
                &nbsp;&nbsp;
                DNS: {status.DNS}
                &nbsp;&nbsp;
                GATEWAY: {status.GATEWAY}
              </p>
              <p className="text-gray-400 text-xs">
                {t('wifiManagement.signalStrength')}: {t('wifiManagement.strong')}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* 网络列表 */}
      <div className="flex items-center justify-between gap-3 mb-4 pb-3 border-b border-white/10">
        <span className="text-white font-semibold">
          {t('wifiManagement.availableNetworks')}
        </span>
        <button
          className="btn-secondary flex items-center gap-2 text-xs"
          onClick={handleScan}
          disabled={scanning}
        >
          <RefreshCw size={14} className={scanning ? 'animate-spin' : ''} />
          {t('wifiManagement.scan')}
        </button>
      </div>

      <div className="max-h-80 overflow-y-auto custom-scrollbar space-y-2 pt-5">
        {networks.map((network, index) => (
          <div
            key={`${network.bssid}-${index}`}
            className={`
              flex items-center justify-between p-4 rounded-lg border transition-all duration-300 cursor-pointer relative
              ${status?.ssid === network.ssid
                ? 'bg-green-500/10 border-green-500/30 cursor-default shadow-lg shadow-green-500/20'
                : 'bg-white/5 border-white/10 hover:bg-blue-500/10 hover:border-blue-500/30 hover:-translate-y-0.5'
              }
            `}
            onClick={() => status?.ssid !== network.ssid && handleConnect(network.ssid, network.is_secure, network.security || undefined)}
          >
            {/* 选中指示器 */}
            {status?.ssid === network.ssid && (
              <div className="absolute left-0 top-0 bottom-0 w-1 bg-green-500 rounded-l-lg"></div>
            )}

            <div className="flex items-center gap-3 flex-1">
              <SignalBars strength={getSignalStrength(network.signal)} />
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-white font-medium text-sm">{network.ssid}</span>
                  {status?.ssid === network.ssid && (
                    <span className="bg-green-500 text-black text-xs font-semibold px-2 py-0.5 rounded">
                      {t('wifiManagement.current')}
                    </span>
                  )}
                </div>
                <div className="text-gray-400 text-xs">
                  {network.security || t('wifiManagement.openNetwork')}
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {network.is_secure ? (
                <Lock className="text-gray-400" size={16} />
              ) : (
                <Unlock className="text-gray-400" size={16} />
              )}

              {status?.ssid === network.ssid ? (
                <div className="flex items-center gap-2 px-3 py-1 rounded-lg bg-green-100 border border-green-300" style={{
                  background: 'rgba(34, 197, 94, 0.2)',
                  border: '1px solid rgba(34, 197, 94, 0.4)'
                }}>
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-green-400 text-xs font-medium">
                    {t('wifiManagement.connectedStatus')}
                  </span>
                </div>
              ) : (
                <button
                  className="btn-primary text-xs px-3 py-1"
                  disabled={connecting === network.ssid}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleConnect(network.ssid, network.is_secure, network.security || undefined);
                  }}
                >
                  {connecting === network.ssid ? (
                    <div className="loading"></div>
                  ) : (
                    t('wifiManagement.connect')
                  )}
                </button>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* WPA3 Blocked Message */}
      {wpa3Blocked && (
        <div className="mt-4 text-center text-red-400 font-semibold text-sm">
          {t('wifiManagement.wpa3Blocked') || 'WPA3 网络暂不支持连接'}
        </div>
      )}

      {/* 断开连接确认对话框 */}
      {showDisconnectConfirm && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-6 max-w-md mx-4 shadow-2xl">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-12 h-12 bg-red-500/20 rounded-full flex items-center justify-center">
                <AlertTriangle size={24} className="text-red-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white mb-1">
                  {t('wifiManagement.disconnect')}
                </h3>
                <p className="text-gray-300 text-sm">
                  {t('confirmDialog.disconnectMessage', { ssid: status?.ssid })}
                </p>
              </div>
            </div>
            <div className="flex gap-3 justify-end">
              <button
                className="btn-secondary"
                onClick={handleDisconnectCancel}
              >
                {t('buttons.cancel') || 'Cancel'}
              </button>
              <button
                className="bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300"
                onClick={handleDisconnectConfirm}
              >
                {t('wifiManagement.disconnect')}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 密码输入对话框 */}
      {passwordDialog.show && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-6 max-w-md mx-4 shadow-2xl">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-blue-400">
                {t('wifiManagement.connect')} {passwordDialog.ssid}
              </h3>
              <button
                onClick={() => setPasswordDialog({ ssid: '', show: false })}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X size={20} />
              </button>
            </div>

            <div className="relative mb-4">
              <input
                type={passwordVisible ? 'text' : 'password'}
                placeholder={t('wifiManagement.enterPassword') || 'Enter password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && password.length > 7 && handlePasswordConnect()}
                className="w-full p-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500 pr-12"
                autoFocus
              />
              <button
                type="button"
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white"
                tabIndex={-1}
                onClick={() => setPasswordVisible((v) => !v)}
              >
                {passwordVisible ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>

            <div className="flex gap-3 justify-end">
              <button
                className="btn-secondary"
                onClick={() => setPasswordDialog({ ssid: '', show: false })}
              >
                {t('buttons.cancel') || 'Cancel'}
              </button>
              <button
                className="btn-primary"
                onClick={handlePasswordConnect}
                disabled={!password}
              >
                {t('wifiManagement.connect')}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default WiFiManagement;
