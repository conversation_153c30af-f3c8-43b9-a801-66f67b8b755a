(function(){const i=document.createElement("link").relList;if(i&&i.supports&&i.supports("modulepreload"))return;for(const f of document.querySelectorAll('link[rel="modulepreload"]'))s(f);new MutationObserver(f=>{for(const o of f)if(o.type==="childList")for(const h of o.addedNodes)h.tagName==="LINK"&&h.rel==="modulepreload"&&s(h)}).observe(document,{childList:!0,subtree:!0});function r(f){const o={};return f.integrity&&(o.integrity=f.integrity),f.referrerPolicy&&(o.referrerPolicy=f.referrerPolicy),f.crossOrigin==="use-credentials"?o.credentials="include":f.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(f){if(f.ep)return;f.ep=!0;const o=r(f);fetch(f.href,o)}})();var Gc={exports:{}},Ll={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var uh;function F0(){if(uh)return Ll;uh=1;var c=Symbol.for("react.transitional.element"),i=Symbol.for("react.fragment");function r(s,f,o){var h=null;if(o!==void 0&&(h=""+o),f.key!==void 0&&(h=""+f.key),"key"in f){o={};for(var p in f)p!=="key"&&(o[p]=f[p])}else o=f;return f=o.ref,{$$typeof:c,type:s,key:h,ref:f!==void 0?f:null,props:o}}return Ll.Fragment=i,Ll.jsx=r,Ll.jsxs=r,Ll}var sh;function W0(){return sh||(sh=1,Gc.exports=F0()),Gc.exports}var z=W0(),Xc={exports:{}},ue={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ch;function P0(){if(ch)return ue;ch=1;var c=Symbol.for("react.transitional.element"),i=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),f=Symbol.for("react.profiler"),o=Symbol.for("react.consumer"),h=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),y=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),E=Symbol.iterator;function w(S){return S===null||typeof S!="object"?null:(S=E&&S[E]||S["@@iterator"],typeof S=="function"?S:null)}var H={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},M=Object.assign,q={};function B(S,C,G){this.props=S,this.context=C,this.refs=q,this.updater=G||H}B.prototype.isReactComponent={},B.prototype.setState=function(S,C){if(typeof S!="object"&&typeof S!="function"&&S!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,S,C,"setState")},B.prototype.forceUpdate=function(S){this.updater.enqueueForceUpdate(this,S,"forceUpdate")};function I(){}I.prototype=B.prototype;function $(S,C,G){this.props=S,this.context=C,this.refs=q,this.updater=G||H}var F=$.prototype=new I;F.constructor=$,M(F,B.prototype),F.isPureReactComponent=!0;var le=Array.isArray,k={H:null,A:null,T:null,S:null,V:null},pe=Object.prototype.hasOwnProperty;function fe(S,C,G,V,J,he){return G=he.ref,{$$typeof:c,type:S,key:C,ref:G!==void 0?G:null,props:he}}function ie(S,C){return fe(S.type,C,void 0,void 0,void 0,S.props)}function se(S){return typeof S=="object"&&S!==null&&S.$$typeof===c}function ge(S){var C={"=":"=0",":":"=2"};return"$"+S.replace(/[=:]/g,function(G){return C[G]})}var ze=/\/+/g;function Be(S,C){return typeof S=="object"&&S!==null&&S.key!=null?ge(""+S.key):C.toString(36)}function qe(){}function K(S){switch(S.status){case"fulfilled":return S.value;case"rejected":throw S.reason;default:switch(typeof S.status=="string"?S.then(qe,qe):(S.status="pending",S.then(function(C){S.status==="pending"&&(S.status="fulfilled",S.value=C)},function(C){S.status==="pending"&&(S.status="rejected",S.reason=C)})),S.status){case"fulfilled":return S.value;case"rejected":throw S.reason}}throw S}function Me(S,C,G,V,J){var he=typeof S;(he==="undefined"||he==="boolean")&&(S=null);var ne=!1;if(S===null)ne=!0;else switch(he){case"bigint":case"string":case"number":ne=!0;break;case"object":switch(S.$$typeof){case c:case i:ne=!0;break;case v:return ne=S._init,Me(ne(S._payload),C,G,V,J)}}if(ne)return J=J(S),ne=V===""?"."+Be(S,0):V,le(J)?(G="",ne!=null&&(G=ne.replace(ze,"$&/")+"/"),Me(J,C,G,"",function(an){return an})):J!=null&&(se(J)&&(J=ie(J,G+(J.key==null||S&&S.key===J.key?"":(""+J.key).replace(ze,"$&/")+"/")+ne)),C.push(J)),1;ne=0;var ot=V===""?".":V+":";if(le(S))for(var _e=0;_e<S.length;_e++)V=S[_e],he=ot+Be(V,_e),ne+=Me(V,C,G,he,J);else if(_e=w(S),typeof _e=="function")for(S=_e.call(S),_e=0;!(V=S.next()).done;)V=V.value,he=ot+Be(V,_e++),ne+=Me(V,C,G,he,J);else if(he==="object"){if(typeof S.then=="function")return Me(K(S),C,G,V,J);throw C=String(S),Error("Objects are not valid as a React child (found: "+(C==="[object Object]"?"object with keys {"+Object.keys(S).join(", ")+"}":C)+"). If you meant to render a collection of children, use an array instead.")}return ne}function _(S,C,G){if(S==null)return S;var V=[],J=0;return Me(S,V,"","",function(he){return C.call(G,he,J++)}),V}function Y(S){if(S._status===-1){var C=S._result;C=C(),C.then(function(G){(S._status===0||S._status===-1)&&(S._status=1,S._result=G)},function(G){(S._status===0||S._status===-1)&&(S._status=2,S._result=G)}),S._status===-1&&(S._status=0,S._result=C)}if(S._status===1)return S._result.default;throw S._result}var Q=typeof reportError=="function"?reportError:function(S){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var C=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof S=="object"&&S!==null&&typeof S.message=="string"?String(S.message):String(S),error:S});if(!window.dispatchEvent(C))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",S);return}console.error(S)};function ye(){}return ue.Children={map:_,forEach:function(S,C,G){_(S,function(){C.apply(this,arguments)},G)},count:function(S){var C=0;return _(S,function(){C++}),C},toArray:function(S){return _(S,function(C){return C})||[]},only:function(S){if(!se(S))throw Error("React.Children.only expected to receive a single React element child.");return S}},ue.Component=B,ue.Fragment=r,ue.Profiler=f,ue.PureComponent=$,ue.StrictMode=s,ue.Suspense=y,ue.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=k,ue.__COMPILER_RUNTIME={__proto__:null,c:function(S){return k.H.useMemoCache(S)}},ue.cache=function(S){return function(){return S.apply(null,arguments)}},ue.cloneElement=function(S,C,G){if(S==null)throw Error("The argument must be a React element, but you passed "+S+".");var V=M({},S.props),J=S.key,he=void 0;if(C!=null)for(ne in C.ref!==void 0&&(he=void 0),C.key!==void 0&&(J=""+C.key),C)!pe.call(C,ne)||ne==="key"||ne==="__self"||ne==="__source"||ne==="ref"&&C.ref===void 0||(V[ne]=C[ne]);var ne=arguments.length-2;if(ne===1)V.children=G;else if(1<ne){for(var ot=Array(ne),_e=0;_e<ne;_e++)ot[_e]=arguments[_e+2];V.children=ot}return fe(S.type,J,void 0,void 0,he,V)},ue.createContext=function(S){return S={$$typeof:h,_currentValue:S,_currentValue2:S,_threadCount:0,Provider:null,Consumer:null},S.Provider=S,S.Consumer={$$typeof:o,_context:S},S},ue.createElement=function(S,C,G){var V,J={},he=null;if(C!=null)for(V in C.key!==void 0&&(he=""+C.key),C)pe.call(C,V)&&V!=="key"&&V!=="__self"&&V!=="__source"&&(J[V]=C[V]);var ne=arguments.length-2;if(ne===1)J.children=G;else if(1<ne){for(var ot=Array(ne),_e=0;_e<ne;_e++)ot[_e]=arguments[_e+2];J.children=ot}if(S&&S.defaultProps)for(V in ne=S.defaultProps,ne)J[V]===void 0&&(J[V]=ne[V]);return fe(S,he,void 0,void 0,null,J)},ue.createRef=function(){return{current:null}},ue.forwardRef=function(S){return{$$typeof:p,render:S}},ue.isValidElement=se,ue.lazy=function(S){return{$$typeof:v,_payload:{_status:-1,_result:S},_init:Y}},ue.memo=function(S,C){return{$$typeof:m,type:S,compare:C===void 0?null:C}},ue.startTransition=function(S){var C=k.T,G={};k.T=G;try{var V=S(),J=k.S;J!==null&&J(G,V),typeof V=="object"&&V!==null&&typeof V.then=="function"&&V.then(ye,Q)}catch(he){Q(he)}finally{k.T=C}},ue.unstable_useCacheRefresh=function(){return k.H.useCacheRefresh()},ue.use=function(S){return k.H.use(S)},ue.useActionState=function(S,C,G){return k.H.useActionState(S,C,G)},ue.useCallback=function(S,C){return k.H.useCallback(S,C)},ue.useContext=function(S){return k.H.useContext(S)},ue.useDebugValue=function(){},ue.useDeferredValue=function(S,C){return k.H.useDeferredValue(S,C)},ue.useEffect=function(S,C,G){var V=k.H;if(typeof G=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return V.useEffect(S,C)},ue.useId=function(){return k.H.useId()},ue.useImperativeHandle=function(S,C,G){return k.H.useImperativeHandle(S,C,G)},ue.useInsertionEffect=function(S,C){return k.H.useInsertionEffect(S,C)},ue.useLayoutEffect=function(S,C){return k.H.useLayoutEffect(S,C)},ue.useMemo=function(S,C){return k.H.useMemo(S,C)},ue.useOptimistic=function(S,C){return k.H.useOptimistic(S,C)},ue.useReducer=function(S,C,G){return k.H.useReducer(S,C,G)},ue.useRef=function(S){return k.H.useRef(S)},ue.useState=function(S){return k.H.useState(S)},ue.useSyncExternalStore=function(S,C,G){return k.H.useSyncExternalStore(S,C,G)},ue.useTransition=function(){return k.H.useTransition()},ue.version="19.1.0",ue}var rh;function cr(){return rh||(rh=1,Xc.exports=P0()),Xc.exports}var Se=cr(),kc={exports:{}},Hl={},Qc={exports:{}},Zc={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fh;function I0(){return fh||(fh=1,function(c){function i(_,Y){var Q=_.length;_.push(Y);e:for(;0<Q;){var ye=Q-1>>>1,S=_[ye];if(0<f(S,Y))_[ye]=Y,_[Q]=S,Q=ye;else break e}}function r(_){return _.length===0?null:_[0]}function s(_){if(_.length===0)return null;var Y=_[0],Q=_.pop();if(Q!==Y){_[0]=Q;e:for(var ye=0,S=_.length,C=S>>>1;ye<C;){var G=2*(ye+1)-1,V=_[G],J=G+1,he=_[J];if(0>f(V,Q))J<S&&0>f(he,V)?(_[ye]=he,_[J]=Q,ye=J):(_[ye]=V,_[G]=Q,ye=G);else if(J<S&&0>f(he,Q))_[ye]=he,_[J]=Q,ye=J;else break e}}return Y}function f(_,Y){var Q=_.sortIndex-Y.sortIndex;return Q!==0?Q:_.id-Y.id}if(c.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var o=performance;c.unstable_now=function(){return o.now()}}else{var h=Date,p=h.now();c.unstable_now=function(){return h.now()-p}}var y=[],m=[],v=1,E=null,w=3,H=!1,M=!1,q=!1,B=!1,I=typeof setTimeout=="function"?setTimeout:null,$=typeof clearTimeout=="function"?clearTimeout:null,F=typeof setImmediate<"u"?setImmediate:null;function le(_){for(var Y=r(m);Y!==null;){if(Y.callback===null)s(m);else if(Y.startTime<=_)s(m),Y.sortIndex=Y.expirationTime,i(y,Y);else break;Y=r(m)}}function k(_){if(q=!1,le(_),!M)if(r(y)!==null)M=!0,pe||(pe=!0,Be());else{var Y=r(m);Y!==null&&Me(k,Y.startTime-_)}}var pe=!1,fe=-1,ie=5,se=-1;function ge(){return B?!0:!(c.unstable_now()-se<ie)}function ze(){if(B=!1,pe){var _=c.unstable_now();se=_;var Y=!0;try{e:{M=!1,q&&(q=!1,$(fe),fe=-1),H=!0;var Q=w;try{t:{for(le(_),E=r(y);E!==null&&!(E.expirationTime>_&&ge());){var ye=E.callback;if(typeof ye=="function"){E.callback=null,w=E.priorityLevel;var S=ye(E.expirationTime<=_);if(_=c.unstable_now(),typeof S=="function"){E.callback=S,le(_),Y=!0;break t}E===r(y)&&s(y),le(_)}else s(y);E=r(y)}if(E!==null)Y=!0;else{var C=r(m);C!==null&&Me(k,C.startTime-_),Y=!1}}break e}finally{E=null,w=Q,H=!1}Y=void 0}}finally{Y?Be():pe=!1}}}var Be;if(typeof F=="function")Be=function(){F(ze)};else if(typeof MessageChannel<"u"){var qe=new MessageChannel,K=qe.port2;qe.port1.onmessage=ze,Be=function(){K.postMessage(null)}}else Be=function(){I(ze,0)};function Me(_,Y){fe=I(function(){_(c.unstable_now())},Y)}c.unstable_IdlePriority=5,c.unstable_ImmediatePriority=1,c.unstable_LowPriority=4,c.unstable_NormalPriority=3,c.unstable_Profiling=null,c.unstable_UserBlockingPriority=2,c.unstable_cancelCallback=function(_){_.callback=null},c.unstable_forceFrameRate=function(_){0>_||125<_?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):ie=0<_?Math.floor(1e3/_):5},c.unstable_getCurrentPriorityLevel=function(){return w},c.unstable_next=function(_){switch(w){case 1:case 2:case 3:var Y=3;break;default:Y=w}var Q=w;w=Y;try{return _()}finally{w=Q}},c.unstable_requestPaint=function(){B=!0},c.unstable_runWithPriority=function(_,Y){switch(_){case 1:case 2:case 3:case 4:case 5:break;default:_=3}var Q=w;w=_;try{return Y()}finally{w=Q}},c.unstable_scheduleCallback=function(_,Y,Q){var ye=c.unstable_now();switch(typeof Q=="object"&&Q!==null?(Q=Q.delay,Q=typeof Q=="number"&&0<Q?ye+Q:ye):Q=ye,_){case 1:var S=-1;break;case 2:S=250;break;case 5:S=1073741823;break;case 4:S=1e4;break;default:S=5e3}return S=Q+S,_={id:v++,callback:Y,priorityLevel:_,startTime:Q,expirationTime:S,sortIndex:-1},Q>ye?(_.sortIndex=Q,i(m,_),r(y)===null&&_===r(m)&&(q?($(fe),fe=-1):q=!0,Me(k,Q-ye))):(_.sortIndex=S,i(y,_),M||H||(M=!0,pe||(pe=!0,Be()))),_},c.unstable_shouldYield=ge,c.unstable_wrapCallback=function(_){var Y=w;return function(){var Q=w;w=Y;try{return _.apply(this,arguments)}finally{w=Q}}}}(Zc)),Zc}var oh;function ep(){return oh||(oh=1,Qc.exports=I0()),Qc.exports}var Kc={exports:{}},tt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dh;function tp(){if(dh)return tt;dh=1;var c=cr();function i(y){var m="https://react.dev/errors/"+y;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var v=2;v<arguments.length;v++)m+="&args[]="+encodeURIComponent(arguments[v])}return"Minified React error #"+y+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function r(){}var s={d:{f:r,r:function(){throw Error(i(522))},D:r,C:r,L:r,m:r,X:r,S:r,M:r},p:0,findDOMNode:null},f=Symbol.for("react.portal");function o(y,m,v){var E=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:f,key:E==null?null:""+E,children:y,containerInfo:m,implementation:v}}var h=c.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function p(y,m){if(y==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return tt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,tt.createPortal=function(y,m){var v=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(i(299));return o(y,m,null,v)},tt.flushSync=function(y){var m=h.T,v=s.p;try{if(h.T=null,s.p=2,y)return y()}finally{h.T=m,s.p=v,s.d.f()}},tt.preconnect=function(y,m){typeof y=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,s.d.C(y,m))},tt.prefetchDNS=function(y){typeof y=="string"&&s.d.D(y)},tt.preinit=function(y,m){if(typeof y=="string"&&m&&typeof m.as=="string"){var v=m.as,E=p(v,m.crossOrigin),w=typeof m.integrity=="string"?m.integrity:void 0,H=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;v==="style"?s.d.S(y,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:E,integrity:w,fetchPriority:H}):v==="script"&&s.d.X(y,{crossOrigin:E,integrity:w,fetchPriority:H,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},tt.preinitModule=function(y,m){if(typeof y=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var v=p(m.as,m.crossOrigin);s.d.M(y,{crossOrigin:v,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&s.d.M(y)},tt.preload=function(y,m){if(typeof y=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var v=m.as,E=p(v,m.crossOrigin);s.d.L(y,v,{crossOrigin:E,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},tt.preloadModule=function(y,m){if(typeof y=="string")if(m){var v=p(m.as,m.crossOrigin);s.d.m(y,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:v,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else s.d.m(y)},tt.requestFormReset=function(y){s.d.r(y)},tt.unstable_batchedUpdates=function(y,m){return y(m)},tt.useFormState=function(y,m,v){return h.H.useFormState(y,m,v)},tt.useFormStatus=function(){return h.H.useHostTransitionStatus()},tt.version="19.1.0",tt}var hh;function np(){if(hh)return Kc.exports;hh=1;function c(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(c)}catch(i){console.error(i)}}return c(),Kc.exports=tp(),Kc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gh;function ap(){if(gh)return Hl;gh=1;var c=ep(),i=cr(),r=np();function s(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function f(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function o(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function h(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function p(e){if(o(e)!==e)throw Error(s(188))}function y(e){var t=e.alternate;if(!t){if(t=o(e),t===null)throw Error(s(188));return t!==e?null:e}for(var n=e,a=t;;){var l=n.return;if(l===null)break;var u=l.alternate;if(u===null){if(a=l.return,a!==null){n=a;continue}break}if(l.child===u.child){for(u=l.child;u;){if(u===n)return p(l),e;if(u===a)return p(l),t;u=u.sibling}throw Error(s(188))}if(n.return!==a.return)n=l,a=u;else{for(var d=!1,g=l.child;g;){if(g===n){d=!0,n=l,a=u;break}if(g===a){d=!0,a=l,n=u;break}g=g.sibling}if(!d){for(g=u.child;g;){if(g===n){d=!0,n=u,a=l;break}if(g===a){d=!0,a=u,n=l;break}g=g.sibling}if(!d)throw Error(s(189))}}if(n.alternate!==a)throw Error(s(190))}if(n.tag!==3)throw Error(s(188));return n.stateNode.current===n?e:t}function m(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=m(e),t!==null)return t;e=e.sibling}return null}var v=Object.assign,E=Symbol.for("react.element"),w=Symbol.for("react.transitional.element"),H=Symbol.for("react.portal"),M=Symbol.for("react.fragment"),q=Symbol.for("react.strict_mode"),B=Symbol.for("react.profiler"),I=Symbol.for("react.provider"),$=Symbol.for("react.consumer"),F=Symbol.for("react.context"),le=Symbol.for("react.forward_ref"),k=Symbol.for("react.suspense"),pe=Symbol.for("react.suspense_list"),fe=Symbol.for("react.memo"),ie=Symbol.for("react.lazy"),se=Symbol.for("react.activity"),ge=Symbol.for("react.memo_cache_sentinel"),ze=Symbol.iterator;function Be(e){return e===null||typeof e!="object"?null:(e=ze&&e[ze]||e["@@iterator"],typeof e=="function"?e:null)}var qe=Symbol.for("react.client.reference");function K(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===qe?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case M:return"Fragment";case B:return"Profiler";case q:return"StrictMode";case k:return"Suspense";case pe:return"SuspenseList";case se:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case H:return"Portal";case F:return(e.displayName||"Context")+".Provider";case $:return(e._context.displayName||"Context")+".Consumer";case le:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case fe:return t=e.displayName||null,t!==null?t:K(e.type)||"Memo";case ie:t=e._payload,e=e._init;try{return K(e(t))}catch{}}return null}var Me=Array.isArray,_=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Y=r.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Q={pending:!1,data:null,method:null,action:null},ye=[],S=-1;function C(e){return{current:e}}function G(e){0>S||(e.current=ye[S],ye[S]=null,S--)}function V(e,t){S++,ye[S]=e.current,e.current=t}var J=C(null),he=C(null),ne=C(null),ot=C(null);function _e(e,t){switch(V(ne,t),V(he,e),V(J,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?jd(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=jd(t),e=Cd(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}G(J),V(J,e)}function an(){G(J),G(he),G(ne)}function Ru(e){e.memoizedState!==null&&V(ot,e);var t=J.current,n=Cd(t,e.type);t!==n&&(V(he,e),V(J,n))}function Jl(e){he.current===e&&(G(J),G(he)),ot.current===e&&(G(ot),zl._currentValue=Q)}var Mu=Object.prototype.hasOwnProperty,_u=c.unstable_scheduleCallback,Du=c.unstable_cancelCallback,Mg=c.unstable_shouldYield,_g=c.unstable_requestPaint,jt=c.unstable_now,Dg=c.unstable_getCurrentPriorityLevel,dr=c.unstable_ImmediatePriority,hr=c.unstable_UserBlockingPriority,$l=c.unstable_NormalPriority,zg=c.unstable_LowPriority,gr=c.unstable_IdlePriority,wg=c.log,Ug=c.unstable_setDisableYieldValue,qa=null,dt=null;function ln(e){if(typeof wg=="function"&&Ug(e),dt&&typeof dt.setStrictMode=="function")try{dt.setStrictMode(qa,e)}catch{}}var ht=Math.clz32?Math.clz32:Lg,jg=Math.log,Cg=Math.LN2;function Lg(e){return e>>>=0,e===0?32:31-(jg(e)/Cg|0)|0}var Fl=256,Wl=4194304;function _n(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Pl(e,t,n){var a=e.pendingLanes;if(a===0)return 0;var l=0,u=e.suspendedLanes,d=e.pingedLanes;e=e.warmLanes;var g=a&134217727;return g!==0?(a=g&~u,a!==0?l=_n(a):(d&=g,d!==0?l=_n(d):n||(n=g&~e,n!==0&&(l=_n(n))))):(g=a&~u,g!==0?l=_n(g):d!==0?l=_n(d):n||(n=a&~e,n!==0&&(l=_n(n)))),l===0?0:t!==0&&t!==l&&(t&u)===0&&(u=l&-l,n=t&-t,u>=n||u===32&&(n&4194048)!==0)?t:l}function Ya(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Hg(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function mr(){var e=Fl;return Fl<<=1,(Fl&4194048)===0&&(Fl=256),e}function pr(){var e=Wl;return Wl<<=1,(Wl&62914560)===0&&(Wl=4194304),e}function zu(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Va(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Bg(e,t,n,a,l,u){var d=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var g=e.entanglements,b=e.expirationTimes,A=e.hiddenUpdates;for(n=d&~n;0<n;){var U=31-ht(n),L=1<<U;g[U]=0,b[U]=-1;var N=A[U];if(N!==null)for(A[U]=null,U=0;U<N.length;U++){var R=N[U];R!==null&&(R.lane&=-536870913)}n&=~L}a!==0&&yr(e,a,0),u!==0&&l===0&&e.tag!==0&&(e.suspendedLanes|=u&~(d&~t))}function yr(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var a=31-ht(t);e.entangledLanes|=t,e.entanglements[a]=e.entanglements[a]|1073741824|n&4194090}function vr(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var a=31-ht(n),l=1<<a;l&t|e[a]&t&&(e[a]|=t),n&=~l}}function wu(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Uu(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function br(){var e=Y.p;return e!==0?e:(e=window.event,e===void 0?32:eh(e.type))}function qg(e,t){var n=Y.p;try{return Y.p=e,t()}finally{Y.p=n}}var un=Math.random().toString(36).slice(2),Ie="__reactFiber$"+un,lt="__reactProps$"+un,Wn="__reactContainer$"+un,ju="__reactEvents$"+un,Yg="__reactListeners$"+un,Vg="__reactHandles$"+un,Sr="__reactResources$"+un,Ga="__reactMarker$"+un;function Cu(e){delete e[Ie],delete e[lt],delete e[ju],delete e[Yg],delete e[Vg]}function Pn(e){var t=e[Ie];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Wn]||n[Ie]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=qd(e);e!==null;){if(n=e[Ie])return n;e=qd(e)}return t}e=n,n=e.parentNode}return null}function In(e){if(e=e[Ie]||e[Wn]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Xa(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(s(33))}function ea(e){var t=e[Sr];return t||(t=e[Sr]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Qe(e){e[Ga]=!0}var xr=new Set,Er={};function Dn(e,t){ta(e,t),ta(e+"Capture",t)}function ta(e,t){for(Er[e]=t,e=0;e<t.length;e++)xr.add(t[e])}var Gg=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Or={},Tr={};function Xg(e){return Mu.call(Tr,e)?!0:Mu.call(Or,e)?!1:Gg.test(e)?Tr[e]=!0:(Or[e]=!0,!1)}function Il(e,t,n){if(Xg(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var a=t.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function ei(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function Gt(e,t,n,a){if(a===null)e.removeAttribute(n);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+a)}}var Lu,Ar;function na(e){if(Lu===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Lu=t&&t[1]||"",Ar=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Lu+e+Ar}var Hu=!1;function Bu(e,t){if(!e||Hu)return"";Hu=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(t){var L=function(){throw Error()};if(Object.defineProperty(L.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(L,[])}catch(R){var N=R}Reflect.construct(e,[],L)}else{try{L.call()}catch(R){N=R}e.call(L.prototype)}}else{try{throw Error()}catch(R){N=R}(L=e())&&typeof L.catch=="function"&&L.catch(function(){})}}catch(R){if(R&&N&&typeof R.stack=="string")return[R.stack,N.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var l=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");l&&l.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=a.DetermineComponentFrameRoot(),d=u[0],g=u[1];if(d&&g){var b=d.split(`
`),A=g.split(`
`);for(l=a=0;a<b.length&&!b[a].includes("DetermineComponentFrameRoot");)a++;for(;l<A.length&&!A[l].includes("DetermineComponentFrameRoot");)l++;if(a===b.length||l===A.length)for(a=b.length-1,l=A.length-1;1<=a&&0<=l&&b[a]!==A[l];)l--;for(;1<=a&&0<=l;a--,l--)if(b[a]!==A[l]){if(a!==1||l!==1)do if(a--,l--,0>l||b[a]!==A[l]){var U=`
`+b[a].replace(" at new "," at ");return e.displayName&&U.includes("<anonymous>")&&(U=U.replace("<anonymous>",e.displayName)),U}while(1<=a&&0<=l);break}}}finally{Hu=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?na(n):""}function kg(e){switch(e.tag){case 26:case 27:case 5:return na(e.type);case 16:return na("Lazy");case 13:return na("Suspense");case 19:return na("SuspenseList");case 0:case 15:return Bu(e.type,!1);case 11:return Bu(e.type.render,!1);case 1:return Bu(e.type,!0);case 31:return na("Activity");default:return""}}function Nr(e){try{var t="";do t+=kg(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function xt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Rr(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Qg(e){var t=Rr(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),a=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,u=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(d){a=""+d,u.call(this,d)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return a},setValue:function(d){a=""+d},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ti(e){e._valueTracker||(e._valueTracker=Qg(e))}function Mr(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),a="";return e&&(a=Rr(e)?e.checked?"true":"false":e.value),e=a,e!==n?(t.setValue(e),!0):!1}function ni(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Zg=/[\n"\\]/g;function Et(e){return e.replace(Zg,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function qu(e,t,n,a,l,u,d,g){e.name="",d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?e.type=d:e.removeAttribute("type"),t!=null?d==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+xt(t)):e.value!==""+xt(t)&&(e.value=""+xt(t)):d!=="submit"&&d!=="reset"||e.removeAttribute("value"),t!=null?Yu(e,d,xt(t)):n!=null?Yu(e,d,xt(n)):a!=null&&e.removeAttribute("value"),l==null&&u!=null&&(e.defaultChecked=!!u),l!=null&&(e.checked=l&&typeof l!="function"&&typeof l!="symbol"),g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"?e.name=""+xt(g):e.removeAttribute("name")}function _r(e,t,n,a,l,u,d,g){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(e.type=u),t!=null||n!=null){if(!(u!=="submit"&&u!=="reset"||t!=null))return;n=n!=null?""+xt(n):"",t=t!=null?""+xt(t):n,g||t===e.value||(e.value=t),e.defaultValue=t}a=a??l,a=typeof a!="function"&&typeof a!="symbol"&&!!a,e.checked=g?e.checked:!!a,e.defaultChecked=!!a,d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"&&(e.name=d)}function Yu(e,t,n){t==="number"&&ni(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function aa(e,t,n,a){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&a&&(e[n].defaultSelected=!0)}else{for(n=""+xt(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,a&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function Dr(e,t,n){if(t!=null&&(t=""+xt(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+xt(n):""}function zr(e,t,n,a){if(t==null){if(a!=null){if(n!=null)throw Error(s(92));if(Me(a)){if(1<a.length)throw Error(s(93));a=a[0]}n=a}n==null&&(n=""),t=n}n=xt(t),e.defaultValue=n,a=e.textContent,a===n&&a!==""&&a!==null&&(e.value=a)}function la(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Kg=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function wr(e,t,n){var a=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?a?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":a?e.setProperty(t,n):typeof n!="number"||n===0||Kg.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Ur(e,t,n){if(t!=null&&typeof t!="object")throw Error(s(62));if(e=e.style,n!=null){for(var a in n)!n.hasOwnProperty(a)||t!=null&&t.hasOwnProperty(a)||(a.indexOf("--")===0?e.setProperty(a,""):a==="float"?e.cssFloat="":e[a]="");for(var l in t)a=t[l],t.hasOwnProperty(l)&&n[l]!==a&&wr(e,l,a)}else for(var u in t)t.hasOwnProperty(u)&&wr(e,u,t[u])}function Vu(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Jg=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),$g=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function ai(e){return $g.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Gu=null;function Xu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ia=null,ua=null;function jr(e){var t=In(e);if(t&&(e=t.stateNode)){var n=e[lt]||null;e:switch(e=t.stateNode,t.type){case"input":if(qu(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Et(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var a=n[t];if(a!==e&&a.form===e.form){var l=a[lt]||null;if(!l)throw Error(s(90));qu(a,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name)}}for(t=0;t<n.length;t++)a=n[t],a.form===e.form&&Mr(a)}break e;case"textarea":Dr(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&aa(e,!!n.multiple,t,!1)}}}var ku=!1;function Cr(e,t,n){if(ku)return e(t,n);ku=!0;try{var a=e(t);return a}finally{if(ku=!1,(ia!==null||ua!==null)&&(Gi(),ia&&(t=ia,e=ua,ua=ia=null,jr(t),e)))for(t=0;t<e.length;t++)jr(e[t])}}function ka(e,t){var n=e.stateNode;if(n===null)return null;var a=n[lt]||null;if(a===null)return null;n=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(e=e.type,a=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!a;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(s(231,t,typeof n));return n}var Xt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Qu=!1;if(Xt)try{var Qa={};Object.defineProperty(Qa,"passive",{get:function(){Qu=!0}}),window.addEventListener("test",Qa,Qa),window.removeEventListener("test",Qa,Qa)}catch{Qu=!1}var sn=null,Zu=null,li=null;function Lr(){if(li)return li;var e,t=Zu,n=t.length,a,l="value"in sn?sn.value:sn.textContent,u=l.length;for(e=0;e<n&&t[e]===l[e];e++);var d=n-e;for(a=1;a<=d&&t[n-a]===l[u-a];a++);return li=l.slice(e,1<a?1-a:void 0)}function ii(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function ui(){return!0}function Hr(){return!1}function it(e){function t(n,a,l,u,d){this._reactName=n,this._targetInst=l,this.type=a,this.nativeEvent=u,this.target=d,this.currentTarget=null;for(var g in e)e.hasOwnProperty(g)&&(n=e[g],this[g]=n?n(u):u[g]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?ui:Hr,this.isPropagationStopped=Hr,this}return v(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=ui)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=ui)},persist:function(){},isPersistent:ui}),t}var zn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},si=it(zn),Za=v({},zn,{view:0,detail:0}),Fg=it(Za),Ku,Ju,Ka,ci=v({},Za,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Fu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Ka&&(Ka&&e.type==="mousemove"?(Ku=e.screenX-Ka.screenX,Ju=e.screenY-Ka.screenY):Ju=Ku=0,Ka=e),Ku)},movementY:function(e){return"movementY"in e?e.movementY:Ju}}),Br=it(ci),Wg=v({},ci,{dataTransfer:0}),Pg=it(Wg),Ig=v({},Za,{relatedTarget:0}),$u=it(Ig),em=v({},zn,{animationName:0,elapsedTime:0,pseudoElement:0}),tm=it(em),nm=v({},zn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),am=it(nm),lm=v({},zn,{data:0}),qr=it(lm),im={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},um={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},sm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function cm(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=sm[e])?!!t[e]:!1}function Fu(){return cm}var rm=v({},Za,{key:function(e){if(e.key){var t=im[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ii(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?um[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Fu,charCode:function(e){return e.type==="keypress"?ii(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ii(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),fm=it(rm),om=v({},ci,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Yr=it(om),dm=v({},Za,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Fu}),hm=it(dm),gm=v({},zn,{propertyName:0,elapsedTime:0,pseudoElement:0}),mm=it(gm),pm=v({},ci,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ym=it(pm),vm=v({},zn,{newState:0,oldState:0}),bm=it(vm),Sm=[9,13,27,32],Wu=Xt&&"CompositionEvent"in window,Ja=null;Xt&&"documentMode"in document&&(Ja=document.documentMode);var xm=Xt&&"TextEvent"in window&&!Ja,Vr=Xt&&(!Wu||Ja&&8<Ja&&11>=Ja),Gr=" ",Xr=!1;function kr(e,t){switch(e){case"keyup":return Sm.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Qr(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var sa=!1;function Em(e,t){switch(e){case"compositionend":return Qr(t);case"keypress":return t.which!==32?null:(Xr=!0,Gr);case"textInput":return e=t.data,e===Gr&&Xr?null:e;default:return null}}function Om(e,t){if(sa)return e==="compositionend"||!Wu&&kr(e,t)?(e=Lr(),li=Zu=sn=null,sa=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Vr&&t.locale!=="ko"?null:t.data;default:return null}}var Tm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Zr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Tm[e.type]:t==="textarea"}function Kr(e,t,n,a){ia?ua?ua.push(a):ua=[a]:ia=a,t=Ji(t,"onChange"),0<t.length&&(n=new si("onChange","change",null,n,a),e.push({event:n,listeners:t}))}var $a=null,Fa=null;function Am(e){_d(e,0)}function ri(e){var t=Xa(e);if(Mr(t))return e}function Jr(e,t){if(e==="change")return t}var $r=!1;if(Xt){var Pu;if(Xt){var Iu="oninput"in document;if(!Iu){var Fr=document.createElement("div");Fr.setAttribute("oninput","return;"),Iu=typeof Fr.oninput=="function"}Pu=Iu}else Pu=!1;$r=Pu&&(!document.documentMode||9<document.documentMode)}function Wr(){$a&&($a.detachEvent("onpropertychange",Pr),Fa=$a=null)}function Pr(e){if(e.propertyName==="value"&&ri(Fa)){var t=[];Kr(t,Fa,e,Xu(e)),Cr(Am,t)}}function Nm(e,t,n){e==="focusin"?(Wr(),$a=t,Fa=n,$a.attachEvent("onpropertychange",Pr)):e==="focusout"&&Wr()}function Rm(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ri(Fa)}function Mm(e,t){if(e==="click")return ri(t)}function _m(e,t){if(e==="input"||e==="change")return ri(t)}function Dm(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var gt=typeof Object.is=="function"?Object.is:Dm;function Wa(e,t){if(gt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),a=Object.keys(t);if(n.length!==a.length)return!1;for(a=0;a<n.length;a++){var l=n[a];if(!Mu.call(t,l)||!gt(e[l],t[l]))return!1}return!0}function Ir(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ef(e,t){var n=Ir(e);e=0;for(var a;n;){if(n.nodeType===3){if(a=e+n.textContent.length,e<=t&&a>=t)return{node:n,offset:t-e};e=a}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ir(n)}}function tf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?tf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function nf(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=ni(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ni(e.document)}return t}function es(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var zm=Xt&&"documentMode"in document&&11>=document.documentMode,ca=null,ts=null,Pa=null,ns=!1;function af(e,t,n){var a=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ns||ca==null||ca!==ni(a)||(a=ca,"selectionStart"in a&&es(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),Pa&&Wa(Pa,a)||(Pa=a,a=Ji(ts,"onSelect"),0<a.length&&(t=new si("onSelect","select",null,t,n),e.push({event:t,listeners:a}),t.target=ca)))}function wn(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ra={animationend:wn("Animation","AnimationEnd"),animationiteration:wn("Animation","AnimationIteration"),animationstart:wn("Animation","AnimationStart"),transitionrun:wn("Transition","TransitionRun"),transitionstart:wn("Transition","TransitionStart"),transitioncancel:wn("Transition","TransitionCancel"),transitionend:wn("Transition","TransitionEnd")},as={},lf={};Xt&&(lf=document.createElement("div").style,"AnimationEvent"in window||(delete ra.animationend.animation,delete ra.animationiteration.animation,delete ra.animationstart.animation),"TransitionEvent"in window||delete ra.transitionend.transition);function Un(e){if(as[e])return as[e];if(!ra[e])return e;var t=ra[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in lf)return as[e]=t[n];return e}var uf=Un("animationend"),sf=Un("animationiteration"),cf=Un("animationstart"),wm=Un("transitionrun"),Um=Un("transitionstart"),jm=Un("transitioncancel"),rf=Un("transitionend"),ff=new Map,ls="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");ls.push("scrollEnd");function Dt(e,t){ff.set(e,t),Dn(t,[e])}var of=new WeakMap;function Ot(e,t){if(typeof e=="object"&&e!==null){var n=of.get(e);return n!==void 0?n:(t={value:e,source:t,stack:Nr(t)},of.set(e,t),t)}return{value:e,source:t,stack:Nr(t)}}var Tt=[],fa=0,is=0;function fi(){for(var e=fa,t=is=fa=0;t<e;){var n=Tt[t];Tt[t++]=null;var a=Tt[t];Tt[t++]=null;var l=Tt[t];Tt[t++]=null;var u=Tt[t];if(Tt[t++]=null,a!==null&&l!==null){var d=a.pending;d===null?l.next=l:(l.next=d.next,d.next=l),a.pending=l}u!==0&&df(n,l,u)}}function oi(e,t,n,a){Tt[fa++]=e,Tt[fa++]=t,Tt[fa++]=n,Tt[fa++]=a,is|=a,e.lanes|=a,e=e.alternate,e!==null&&(e.lanes|=a)}function us(e,t,n,a){return oi(e,t,n,a),di(e)}function oa(e,t){return oi(e,null,null,t),di(e)}function df(e,t,n){e.lanes|=n;var a=e.alternate;a!==null&&(a.lanes|=n);for(var l=!1,u=e.return;u!==null;)u.childLanes|=n,a=u.alternate,a!==null&&(a.childLanes|=n),u.tag===22&&(e=u.stateNode,e===null||e._visibility&1||(l=!0)),e=u,u=u.return;return e.tag===3?(u=e.stateNode,l&&t!==null&&(l=31-ht(n),e=u.hiddenUpdates,a=e[l],a===null?e[l]=[t]:a.push(t),t.lane=n|536870912),u):null}function di(e){if(50<Ol)throw Ol=0,dc=null,Error(s(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var da={};function Cm(e,t,n,a){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function mt(e,t,n,a){return new Cm(e,t,n,a)}function ss(e){return e=e.prototype,!(!e||!e.isReactComponent)}function kt(e,t){var n=e.alternate;return n===null?(n=mt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function hf(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function hi(e,t,n,a,l,u){var d=0;if(a=e,typeof e=="function")ss(e)&&(d=1);else if(typeof e=="string")d=H0(e,n,J.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case se:return e=mt(31,n,t,l),e.elementType=se,e.lanes=u,e;case M:return jn(n.children,l,u,t);case q:d=8,l|=24;break;case B:return e=mt(12,n,t,l|2),e.elementType=B,e.lanes=u,e;case k:return e=mt(13,n,t,l),e.elementType=k,e.lanes=u,e;case pe:return e=mt(19,n,t,l),e.elementType=pe,e.lanes=u,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case I:case F:d=10;break e;case $:d=9;break e;case le:d=11;break e;case fe:d=14;break e;case ie:d=16,a=null;break e}d=29,n=Error(s(130,e===null?"null":typeof e,"")),a=null}return t=mt(d,n,t,l),t.elementType=e,t.type=a,t.lanes=u,t}function jn(e,t,n,a){return e=mt(7,e,a,t),e.lanes=n,e}function cs(e,t,n){return e=mt(6,e,null,t),e.lanes=n,e}function rs(e,t,n){return t=mt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var ha=[],ga=0,gi=null,mi=0,At=[],Nt=0,Cn=null,Qt=1,Zt="";function Ln(e,t){ha[ga++]=mi,ha[ga++]=gi,gi=e,mi=t}function gf(e,t,n){At[Nt++]=Qt,At[Nt++]=Zt,At[Nt++]=Cn,Cn=e;var a=Qt;e=Zt;var l=32-ht(a)-1;a&=~(1<<l),n+=1;var u=32-ht(t)+l;if(30<u){var d=l-l%5;u=(a&(1<<d)-1).toString(32),a>>=d,l-=d,Qt=1<<32-ht(t)+l|n<<l|a,Zt=u+e}else Qt=1<<u|n<<l|a,Zt=e}function fs(e){e.return!==null&&(Ln(e,1),gf(e,1,0))}function os(e){for(;e===gi;)gi=ha[--ga],ha[ga]=null,mi=ha[--ga],ha[ga]=null;for(;e===Cn;)Cn=At[--Nt],At[Nt]=null,Zt=At[--Nt],At[Nt]=null,Qt=At[--Nt],At[Nt]=null}var at=null,Ue=null,be=!1,Hn=null,Ct=!1,ds=Error(s(519));function Bn(e){var t=Error(s(418,""));throw tl(Ot(t,e)),ds}function mf(e){var t=e.stateNode,n=e.type,a=e.memoizedProps;switch(t[Ie]=e,t[lt]=a,n){case"dialog":de("cancel",t),de("close",t);break;case"iframe":case"object":case"embed":de("load",t);break;case"video":case"audio":for(n=0;n<Al.length;n++)de(Al[n],t);break;case"source":de("error",t);break;case"img":case"image":case"link":de("error",t),de("load",t);break;case"details":de("toggle",t);break;case"input":de("invalid",t),_r(t,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),ti(t);break;case"select":de("invalid",t);break;case"textarea":de("invalid",t),zr(t,a.value,a.defaultValue,a.children),ti(t)}n=a.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||a.suppressHydrationWarning===!0||Ud(t.textContent,n)?(a.popover!=null&&(de("beforetoggle",t),de("toggle",t)),a.onScroll!=null&&de("scroll",t),a.onScrollEnd!=null&&de("scrollend",t),a.onClick!=null&&(t.onclick=$i),t=!0):t=!1,t||Bn(e)}function pf(e){for(at=e.return;at;)switch(at.tag){case 5:case 13:Ct=!1;return;case 27:case 3:Ct=!0;return;default:at=at.return}}function Ia(e){if(e!==at)return!1;if(!be)return pf(e),be=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||Mc(e.type,e.memoizedProps)),n=!n),n&&Ue&&Bn(e),pf(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(s(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){Ue=wt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}Ue=null}}else t===27?(t=Ue,On(e.type)?(e=wc,wc=null,Ue=e):Ue=t):Ue=at?wt(e.stateNode.nextSibling):null;return!0}function el(){Ue=at=null,be=!1}function yf(){var e=Hn;return e!==null&&(ct===null?ct=e:ct.push.apply(ct,e),Hn=null),e}function tl(e){Hn===null?Hn=[e]:Hn.push(e)}var hs=C(null),qn=null,Kt=null;function cn(e,t,n){V(hs,t._currentValue),t._currentValue=n}function Jt(e){e._currentValue=hs.current,G(hs)}function gs(e,t,n){for(;e!==null;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,a!==null&&(a.childLanes|=t)):a!==null&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===n)break;e=e.return}}function ms(e,t,n,a){var l=e.child;for(l!==null&&(l.return=e);l!==null;){var u=l.dependencies;if(u!==null){var d=l.child;u=u.firstContext;e:for(;u!==null;){var g=u;u=l;for(var b=0;b<t.length;b++)if(g.context===t[b]){u.lanes|=n,g=u.alternate,g!==null&&(g.lanes|=n),gs(u.return,n,e),a||(d=null);break e}u=g.next}}else if(l.tag===18){if(d=l.return,d===null)throw Error(s(341));d.lanes|=n,u=d.alternate,u!==null&&(u.lanes|=n),gs(d,n,e),d=null}else d=l.child;if(d!==null)d.return=l;else for(d=l;d!==null;){if(d===e){d=null;break}if(l=d.sibling,l!==null){l.return=d.return,d=l;break}d=d.return}l=d}}function nl(e,t,n,a){e=null;for(var l=t,u=!1;l!==null;){if(!u){if((l.flags&524288)!==0)u=!0;else if((l.flags&262144)!==0)break}if(l.tag===10){var d=l.alternate;if(d===null)throw Error(s(387));if(d=d.memoizedProps,d!==null){var g=l.type;gt(l.pendingProps.value,d.value)||(e!==null?e.push(g):e=[g])}}else if(l===ot.current){if(d=l.alternate,d===null)throw Error(s(387));d.memoizedState.memoizedState!==l.memoizedState.memoizedState&&(e!==null?e.push(zl):e=[zl])}l=l.return}e!==null&&ms(t,e,n,a),t.flags|=262144}function pi(e){for(e=e.firstContext;e!==null;){if(!gt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Yn(e){qn=e,Kt=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function et(e){return vf(qn,e)}function yi(e,t){return qn===null&&Yn(e),vf(e,t)}function vf(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},Kt===null){if(e===null)throw Error(s(308));Kt=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Kt=Kt.next=t;return n}var Lm=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,a){e.push(a)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},Hm=c.unstable_scheduleCallback,Bm=c.unstable_NormalPriority,Xe={$$typeof:F,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function ps(){return{controller:new Lm,data:new Map,refCount:0}}function al(e){e.refCount--,e.refCount===0&&Hm(Bm,function(){e.controller.abort()})}var ll=null,ys=0,ma=0,pa=null;function qm(e,t){if(ll===null){var n=ll=[];ys=0,ma=bc(),pa={status:"pending",value:void 0,then:function(a){n.push(a)}}}return ys++,t.then(bf,bf),t}function bf(){if(--ys===0&&ll!==null){pa!==null&&(pa.status="fulfilled");var e=ll;ll=null,ma=0,pa=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function Ym(e,t){var n=[],a={status:"pending",value:null,reason:null,then:function(l){n.push(l)}};return e.then(function(){a.status="fulfilled",a.value=t;for(var l=0;l<n.length;l++)(0,n[l])(t)},function(l){for(a.status="rejected",a.reason=l,l=0;l<n.length;l++)(0,n[l])(void 0)}),a}var Sf=_.S;_.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&qm(e,t),Sf!==null&&Sf(e,t)};var Vn=C(null);function vs(){var e=Vn.current;return e!==null?e:Re.pooledCache}function vi(e,t){t===null?V(Vn,Vn.current):V(Vn,t.pool)}function xf(){var e=vs();return e===null?null:{parent:Xe._currentValue,pool:e}}var il=Error(s(460)),Ef=Error(s(474)),bi=Error(s(542)),bs={then:function(){}};function Of(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Si(){}function Tf(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(Si,Si),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Nf(e),e;default:if(typeof t.status=="string")t.then(Si,Si);else{if(e=Re,e!==null&&100<e.shellSuspendCounter)throw Error(s(482));e=t,e.status="pending",e.then(function(a){if(t.status==="pending"){var l=t;l.status="fulfilled",l.value=a}},function(a){if(t.status==="pending"){var l=t;l.status="rejected",l.reason=a}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Nf(e),e}throw ul=t,il}}var ul=null;function Af(){if(ul===null)throw Error(s(459));var e=ul;return ul=null,e}function Nf(e){if(e===il||e===bi)throw Error(s(483))}var rn=!1;function Ss(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function xs(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function fn(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function on(e,t,n){var a=e.updateQueue;if(a===null)return null;if(a=a.shared,(xe&2)!==0){var l=a.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),a.pending=t,t=di(e),df(e,null,n),t}return oi(e,a,t,n),di(e)}function sl(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var a=t.lanes;a&=e.pendingLanes,n|=a,t.lanes=n,vr(e,n)}}function Es(e,t){var n=e.updateQueue,a=e.alternate;if(a!==null&&(a=a.updateQueue,n===a)){var l=null,u=null;if(n=n.firstBaseUpdate,n!==null){do{var d={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};u===null?l=u=d:u=u.next=d,n=n.next}while(n!==null);u===null?l=u=t:u=u.next=t}else l=u=t;n={baseState:a.baseState,firstBaseUpdate:l,lastBaseUpdate:u,shared:a.shared,callbacks:a.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var Os=!1;function cl(){if(Os){var e=pa;if(e!==null)throw e}}function rl(e,t,n,a){Os=!1;var l=e.updateQueue;rn=!1;var u=l.firstBaseUpdate,d=l.lastBaseUpdate,g=l.shared.pending;if(g!==null){l.shared.pending=null;var b=g,A=b.next;b.next=null,d===null?u=A:d.next=A,d=b;var U=e.alternate;U!==null&&(U=U.updateQueue,g=U.lastBaseUpdate,g!==d&&(g===null?U.firstBaseUpdate=A:g.next=A,U.lastBaseUpdate=b))}if(u!==null){var L=l.baseState;d=0,U=A=b=null,g=u;do{var N=g.lane&-536870913,R=N!==g.lane;if(R?(me&N)===N:(a&N)===N){N!==0&&N===ma&&(Os=!0),U!==null&&(U=U.next={lane:0,tag:g.tag,payload:g.payload,callback:null,next:null});e:{var ee=e,W=g;N=t;var Ae=n;switch(W.tag){case 1:if(ee=W.payload,typeof ee=="function"){L=ee.call(Ae,L,N);break e}L=ee;break e;case 3:ee.flags=ee.flags&-65537|128;case 0:if(ee=W.payload,N=typeof ee=="function"?ee.call(Ae,L,N):ee,N==null)break e;L=v({},L,N);break e;case 2:rn=!0}}N=g.callback,N!==null&&(e.flags|=64,R&&(e.flags|=8192),R=l.callbacks,R===null?l.callbacks=[N]:R.push(N))}else R={lane:N,tag:g.tag,payload:g.payload,callback:g.callback,next:null},U===null?(A=U=R,b=L):U=U.next=R,d|=N;if(g=g.next,g===null){if(g=l.shared.pending,g===null)break;R=g,g=R.next,R.next=null,l.lastBaseUpdate=R,l.shared.pending=null}}while(!0);U===null&&(b=L),l.baseState=b,l.firstBaseUpdate=A,l.lastBaseUpdate=U,u===null&&(l.shared.lanes=0),bn|=d,e.lanes=d,e.memoizedState=L}}function Rf(e,t){if(typeof e!="function")throw Error(s(191,e));e.call(t)}function Mf(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)Rf(n[e],t)}var ya=C(null),xi=C(0);function _f(e,t){e=tn,V(xi,e),V(ya,t),tn=e|t.baseLanes}function Ts(){V(xi,tn),V(ya,ya.current)}function As(){tn=xi.current,G(ya),G(xi)}var dn=0,ce=null,Oe=null,Ye=null,Ei=!1,va=!1,Gn=!1,Oi=0,fl=0,ba=null,Vm=0;function Ce(){throw Error(s(321))}function Ns(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!gt(e[n],t[n]))return!1;return!0}function Rs(e,t,n,a,l,u){return dn=u,ce=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,_.H=e===null||e.memoizedState===null?ho:go,Gn=!1,u=n(a,l),Gn=!1,va&&(u=zf(t,n,a,l)),Df(e),u}function Df(e){_.H=_i;var t=Oe!==null&&Oe.next!==null;if(dn=0,Ye=Oe=ce=null,Ei=!1,fl=0,ba=null,t)throw Error(s(300));e===null||Ze||(e=e.dependencies,e!==null&&pi(e)&&(Ze=!0))}function zf(e,t,n,a){ce=e;var l=0;do{if(va&&(ba=null),fl=0,va=!1,25<=l)throw Error(s(301));if(l+=1,Ye=Oe=null,e.updateQueue!=null){var u=e.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}_.H=Jm,u=t(n,a)}while(va);return u}function Gm(){var e=_.H,t=e.useState()[0];return t=typeof t.then=="function"?ol(t):t,e=e.useState()[0],(Oe!==null?Oe.memoizedState:null)!==e&&(ce.flags|=1024),t}function Ms(){var e=Oi!==0;return Oi=0,e}function _s(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Ds(e){if(Ei){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Ei=!1}dn=0,Ye=Oe=ce=null,va=!1,fl=Oi=0,ba=null}function ut(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ye===null?ce.memoizedState=Ye=e:Ye=Ye.next=e,Ye}function Ve(){if(Oe===null){var e=ce.alternate;e=e!==null?e.memoizedState:null}else e=Oe.next;var t=Ye===null?ce.memoizedState:Ye.next;if(t!==null)Ye=t,Oe=e;else{if(e===null)throw ce.alternate===null?Error(s(467)):Error(s(310));Oe=e,e={memoizedState:Oe.memoizedState,baseState:Oe.baseState,baseQueue:Oe.baseQueue,queue:Oe.queue,next:null},Ye===null?ce.memoizedState=Ye=e:Ye=Ye.next=e}return Ye}function zs(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function ol(e){var t=fl;return fl+=1,ba===null&&(ba=[]),e=Tf(ba,e,t),t=ce,(Ye===null?t.memoizedState:Ye.next)===null&&(t=t.alternate,_.H=t===null||t.memoizedState===null?ho:go),e}function Ti(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return ol(e);if(e.$$typeof===F)return et(e)}throw Error(s(438,String(e)))}function ws(e){var t=null,n=ce.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var a=ce.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(t={data:a.data.map(function(l){return l.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=zs(),ce.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),a=0;a<e;a++)n[a]=ge;return t.index++,n}function $t(e,t){return typeof t=="function"?t(e):t}function Ai(e){var t=Ve();return Us(t,Oe,e)}function Us(e,t,n){var a=e.queue;if(a===null)throw Error(s(311));a.lastRenderedReducer=n;var l=e.baseQueue,u=a.pending;if(u!==null){if(l!==null){var d=l.next;l.next=u.next,u.next=d}t.baseQueue=l=u,a.pending=null}if(u=e.baseState,l===null)e.memoizedState=u;else{t=l.next;var g=d=null,b=null,A=t,U=!1;do{var L=A.lane&-536870913;if(L!==A.lane?(me&L)===L:(dn&L)===L){var N=A.revertLane;if(N===0)b!==null&&(b=b.next={lane:0,revertLane:0,action:A.action,hasEagerState:A.hasEagerState,eagerState:A.eagerState,next:null}),L===ma&&(U=!0);else if((dn&N)===N){A=A.next,N===ma&&(U=!0);continue}else L={lane:0,revertLane:A.revertLane,action:A.action,hasEagerState:A.hasEagerState,eagerState:A.eagerState,next:null},b===null?(g=b=L,d=u):b=b.next=L,ce.lanes|=N,bn|=N;L=A.action,Gn&&n(u,L),u=A.hasEagerState?A.eagerState:n(u,L)}else N={lane:L,revertLane:A.revertLane,action:A.action,hasEagerState:A.hasEagerState,eagerState:A.eagerState,next:null},b===null?(g=b=N,d=u):b=b.next=N,ce.lanes|=L,bn|=L;A=A.next}while(A!==null&&A!==t);if(b===null?d=u:b.next=g,!gt(u,e.memoizedState)&&(Ze=!0,U&&(n=pa,n!==null)))throw n;e.memoizedState=u,e.baseState=d,e.baseQueue=b,a.lastRenderedState=u}return l===null&&(a.lanes=0),[e.memoizedState,a.dispatch]}function js(e){var t=Ve(),n=t.queue;if(n===null)throw Error(s(311));n.lastRenderedReducer=e;var a=n.dispatch,l=n.pending,u=t.memoizedState;if(l!==null){n.pending=null;var d=l=l.next;do u=e(u,d.action),d=d.next;while(d!==l);gt(u,t.memoizedState)||(Ze=!0),t.memoizedState=u,t.baseQueue===null&&(t.baseState=u),n.lastRenderedState=u}return[u,a]}function wf(e,t,n){var a=ce,l=Ve(),u=be;if(u){if(n===void 0)throw Error(s(407));n=n()}else n=t();var d=!gt((Oe||l).memoizedState,n);d&&(l.memoizedState=n,Ze=!0),l=l.queue;var g=Cf.bind(null,a,l,e);if(dl(2048,8,g,[e]),l.getSnapshot!==t||d||Ye!==null&&Ye.memoizedState.tag&1){if(a.flags|=2048,Sa(9,Ni(),jf.bind(null,a,l,n,t),null),Re===null)throw Error(s(349));u||(dn&124)!==0||Uf(a,t,n)}return n}function Uf(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ce.updateQueue,t===null?(t=zs(),ce.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function jf(e,t,n,a){t.value=n,t.getSnapshot=a,Lf(t)&&Hf(e)}function Cf(e,t,n){return n(function(){Lf(t)&&Hf(e)})}function Lf(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!gt(e,n)}catch{return!0}}function Hf(e){var t=oa(e,2);t!==null&&St(t,e,2)}function Cs(e){var t=ut();if(typeof e=="function"){var n=e;if(e=n(),Gn){ln(!0);try{n()}finally{ln(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:$t,lastRenderedState:e},t}function Bf(e,t,n,a){return e.baseState=n,Us(e,Oe,typeof a=="function"?a:$t)}function Xm(e,t,n,a,l){if(Mi(e))throw Error(s(485));if(e=t.action,e!==null){var u={payload:l,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(d){u.listeners.push(d)}};_.T!==null?n(!0):u.isTransition=!1,a(u),n=t.pending,n===null?(u.next=t.pending=u,qf(t,u)):(u.next=n.next,t.pending=n.next=u)}}function qf(e,t){var n=t.action,a=t.payload,l=e.state;if(t.isTransition){var u=_.T,d={};_.T=d;try{var g=n(l,a),b=_.S;b!==null&&b(d,g),Yf(e,t,g)}catch(A){Ls(e,t,A)}finally{_.T=u}}else try{u=n(l,a),Yf(e,t,u)}catch(A){Ls(e,t,A)}}function Yf(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(a){Vf(e,t,a)},function(a){return Ls(e,t,a)}):Vf(e,t,n)}function Vf(e,t,n){t.status="fulfilled",t.value=n,Gf(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,qf(e,n)))}function Ls(e,t,n){var a=e.pending;if(e.pending=null,a!==null){a=a.next;do t.status="rejected",t.reason=n,Gf(t),t=t.next;while(t!==a)}e.action=null}function Gf(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Xf(e,t){return t}function kf(e,t){if(be){var n=Re.formState;if(n!==null){e:{var a=ce;if(be){if(Ue){t:{for(var l=Ue,u=Ct;l.nodeType!==8;){if(!u){l=null;break t}if(l=wt(l.nextSibling),l===null){l=null;break t}}u=l.data,l=u==="F!"||u==="F"?l:null}if(l){Ue=wt(l.nextSibling),a=l.data==="F!";break e}}Bn(a)}a=!1}a&&(t=n[0])}}return n=ut(),n.memoizedState=n.baseState=t,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Xf,lastRenderedState:t},n.queue=a,n=ro.bind(null,ce,a),a.dispatch=n,a=Cs(!1),u=Vs.bind(null,ce,!1,a.queue),a=ut(),l={state:t,dispatch:null,action:e,pending:null},a.queue=l,n=Xm.bind(null,ce,l,u,n),l.dispatch=n,a.memoizedState=e,[t,n,!1]}function Qf(e){var t=Ve();return Zf(t,Oe,e)}function Zf(e,t,n){if(t=Us(e,t,Xf)[0],e=Ai($t)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var a=ol(t)}catch(d){throw d===il?bi:d}else a=t;t=Ve();var l=t.queue,u=l.dispatch;return n!==t.memoizedState&&(ce.flags|=2048,Sa(9,Ni(),km.bind(null,l,n),null)),[a,u,e]}function km(e,t){e.action=t}function Kf(e){var t=Ve(),n=Oe;if(n!==null)return Zf(t,n,e);Ve(),t=t.memoizedState,n=Ve();var a=n.queue.dispatch;return n.memoizedState=e,[t,a,!1]}function Sa(e,t,n,a){return e={tag:e,create:n,deps:a,inst:t,next:null},t=ce.updateQueue,t===null&&(t=zs(),ce.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(a=n.next,n.next=e,e.next=a,t.lastEffect=e),e}function Ni(){return{destroy:void 0,resource:void 0}}function Jf(){return Ve().memoizedState}function Ri(e,t,n,a){var l=ut();a=a===void 0?null:a,ce.flags|=e,l.memoizedState=Sa(1|t,Ni(),n,a)}function dl(e,t,n,a){var l=Ve();a=a===void 0?null:a;var u=l.memoizedState.inst;Oe!==null&&a!==null&&Ns(a,Oe.memoizedState.deps)?l.memoizedState=Sa(t,u,n,a):(ce.flags|=e,l.memoizedState=Sa(1|t,u,n,a))}function $f(e,t){Ri(8390656,8,e,t)}function Ff(e,t){dl(2048,8,e,t)}function Wf(e,t){return dl(4,2,e,t)}function Pf(e,t){return dl(4,4,e,t)}function If(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function eo(e,t,n){n=n!=null?n.concat([e]):null,dl(4,4,If.bind(null,t,e),n)}function Hs(){}function to(e,t){var n=Ve();t=t===void 0?null:t;var a=n.memoizedState;return t!==null&&Ns(t,a[1])?a[0]:(n.memoizedState=[e,t],e)}function no(e,t){var n=Ve();t=t===void 0?null:t;var a=n.memoizedState;if(t!==null&&Ns(t,a[1]))return a[0];if(a=e(),Gn){ln(!0);try{e()}finally{ln(!1)}}return n.memoizedState=[a,t],a}function Bs(e,t,n){return n===void 0||(dn&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=ud(),ce.lanes|=e,bn|=e,n)}function ao(e,t,n,a){return gt(n,t)?n:ya.current!==null?(e=Bs(e,n,a),gt(e,t)||(Ze=!0),e):(dn&42)===0?(Ze=!0,e.memoizedState=n):(e=ud(),ce.lanes|=e,bn|=e,t)}function lo(e,t,n,a,l){var u=Y.p;Y.p=u!==0&&8>u?u:8;var d=_.T,g={};_.T=g,Vs(e,!1,t,n);try{var b=l(),A=_.S;if(A!==null&&A(g,b),b!==null&&typeof b=="object"&&typeof b.then=="function"){var U=Ym(b,a);hl(e,t,U,bt(e))}else hl(e,t,a,bt(e))}catch(L){hl(e,t,{then:function(){},status:"rejected",reason:L},bt())}finally{Y.p=u,_.T=d}}function Qm(){}function qs(e,t,n,a){if(e.tag!==5)throw Error(s(476));var l=io(e).queue;lo(e,l,t,Q,n===null?Qm:function(){return uo(e),n(a)})}function io(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:Q,baseState:Q,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:$t,lastRenderedState:Q},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:$t,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function uo(e){var t=io(e).next.queue;hl(e,t,{},bt())}function Ys(){return et(zl)}function so(){return Ve().memoizedState}function co(){return Ve().memoizedState}function Zm(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=bt();e=fn(n);var a=on(t,e,n);a!==null&&(St(a,t,n),sl(a,t,n)),t={cache:ps()},e.payload=t;return}t=t.return}}function Km(e,t,n){var a=bt();n={lane:a,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Mi(e)?fo(t,n):(n=us(e,t,n,a),n!==null&&(St(n,e,a),oo(n,t,a)))}function ro(e,t,n){var a=bt();hl(e,t,n,a)}function hl(e,t,n,a){var l={lane:a,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Mi(e))fo(t,l);else{var u=e.alternate;if(e.lanes===0&&(u===null||u.lanes===0)&&(u=t.lastRenderedReducer,u!==null))try{var d=t.lastRenderedState,g=u(d,n);if(l.hasEagerState=!0,l.eagerState=g,gt(g,d))return oi(e,t,l,0),Re===null&&fi(),!1}catch{}finally{}if(n=us(e,t,l,a),n!==null)return St(n,e,a),oo(n,t,a),!0}return!1}function Vs(e,t,n,a){if(a={lane:2,revertLane:bc(),action:a,hasEagerState:!1,eagerState:null,next:null},Mi(e)){if(t)throw Error(s(479))}else t=us(e,n,a,2),t!==null&&St(t,e,2)}function Mi(e){var t=e.alternate;return e===ce||t!==null&&t===ce}function fo(e,t){va=Ei=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function oo(e,t,n){if((n&4194048)!==0){var a=t.lanes;a&=e.pendingLanes,n|=a,t.lanes=n,vr(e,n)}}var _i={readContext:et,use:Ti,useCallback:Ce,useContext:Ce,useEffect:Ce,useImperativeHandle:Ce,useLayoutEffect:Ce,useInsertionEffect:Ce,useMemo:Ce,useReducer:Ce,useRef:Ce,useState:Ce,useDebugValue:Ce,useDeferredValue:Ce,useTransition:Ce,useSyncExternalStore:Ce,useId:Ce,useHostTransitionStatus:Ce,useFormState:Ce,useActionState:Ce,useOptimistic:Ce,useMemoCache:Ce,useCacheRefresh:Ce},ho={readContext:et,use:Ti,useCallback:function(e,t){return ut().memoizedState=[e,t===void 0?null:t],e},useContext:et,useEffect:$f,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,Ri(4194308,4,If.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ri(4194308,4,e,t)},useInsertionEffect:function(e,t){Ri(4,2,e,t)},useMemo:function(e,t){var n=ut();t=t===void 0?null:t;var a=e();if(Gn){ln(!0);try{e()}finally{ln(!1)}}return n.memoizedState=[a,t],a},useReducer:function(e,t,n){var a=ut();if(n!==void 0){var l=n(t);if(Gn){ln(!0);try{n(t)}finally{ln(!1)}}}else l=t;return a.memoizedState=a.baseState=l,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:l},a.queue=e,e=e.dispatch=Km.bind(null,ce,e),[a.memoizedState,e]},useRef:function(e){var t=ut();return e={current:e},t.memoizedState=e},useState:function(e){e=Cs(e);var t=e.queue,n=ro.bind(null,ce,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Hs,useDeferredValue:function(e,t){var n=ut();return Bs(n,e,t)},useTransition:function(){var e=Cs(!1);return e=lo.bind(null,ce,e.queue,!0,!1),ut().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var a=ce,l=ut();if(be){if(n===void 0)throw Error(s(407));n=n()}else{if(n=t(),Re===null)throw Error(s(349));(me&124)!==0||Uf(a,t,n)}l.memoizedState=n;var u={value:n,getSnapshot:t};return l.queue=u,$f(Cf.bind(null,a,u,e),[e]),a.flags|=2048,Sa(9,Ni(),jf.bind(null,a,u,n,t),null),n},useId:function(){var e=ut(),t=Re.identifierPrefix;if(be){var n=Zt,a=Qt;n=(a&~(1<<32-ht(a)-1)).toString(32)+n,t="«"+t+"R"+n,n=Oi++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=Vm++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Ys,useFormState:kf,useActionState:kf,useOptimistic:function(e){var t=ut();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Vs.bind(null,ce,!0,n),n.dispatch=t,[e,t]},useMemoCache:ws,useCacheRefresh:function(){return ut().memoizedState=Zm.bind(null,ce)}},go={readContext:et,use:Ti,useCallback:to,useContext:et,useEffect:Ff,useImperativeHandle:eo,useInsertionEffect:Wf,useLayoutEffect:Pf,useMemo:no,useReducer:Ai,useRef:Jf,useState:function(){return Ai($t)},useDebugValue:Hs,useDeferredValue:function(e,t){var n=Ve();return ao(n,Oe.memoizedState,e,t)},useTransition:function(){var e=Ai($t)[0],t=Ve().memoizedState;return[typeof e=="boolean"?e:ol(e),t]},useSyncExternalStore:wf,useId:so,useHostTransitionStatus:Ys,useFormState:Qf,useActionState:Qf,useOptimistic:function(e,t){var n=Ve();return Bf(n,Oe,e,t)},useMemoCache:ws,useCacheRefresh:co},Jm={readContext:et,use:Ti,useCallback:to,useContext:et,useEffect:Ff,useImperativeHandle:eo,useInsertionEffect:Wf,useLayoutEffect:Pf,useMemo:no,useReducer:js,useRef:Jf,useState:function(){return js($t)},useDebugValue:Hs,useDeferredValue:function(e,t){var n=Ve();return Oe===null?Bs(n,e,t):ao(n,Oe.memoizedState,e,t)},useTransition:function(){var e=js($t)[0],t=Ve().memoizedState;return[typeof e=="boolean"?e:ol(e),t]},useSyncExternalStore:wf,useId:so,useHostTransitionStatus:Ys,useFormState:Kf,useActionState:Kf,useOptimistic:function(e,t){var n=Ve();return Oe!==null?Bf(n,Oe,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:ws,useCacheRefresh:co},xa=null,gl=0;function Di(e){var t=gl;return gl+=1,xa===null&&(xa=[]),Tf(xa,e,t)}function ml(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function zi(e,t){throw t.$$typeof===E?Error(s(525)):(e=Object.prototype.toString.call(t),Error(s(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function mo(e){var t=e._init;return t(e._payload)}function po(e){function t(O,x){if(e){var T=O.deletions;T===null?(O.deletions=[x],O.flags|=16):T.push(x)}}function n(O,x){if(!e)return null;for(;x!==null;)t(O,x),x=x.sibling;return null}function a(O){for(var x=new Map;O!==null;)O.key!==null?x.set(O.key,O):x.set(O.index,O),O=O.sibling;return x}function l(O,x){return O=kt(O,x),O.index=0,O.sibling=null,O}function u(O,x,T){return O.index=T,e?(T=O.alternate,T!==null?(T=T.index,T<x?(O.flags|=67108866,x):T):(O.flags|=67108866,x)):(O.flags|=1048576,x)}function d(O){return e&&O.alternate===null&&(O.flags|=67108866),O}function g(O,x,T,j){return x===null||x.tag!==6?(x=cs(T,O.mode,j),x.return=O,x):(x=l(x,T),x.return=O,x)}function b(O,x,T,j){var X=T.type;return X===M?U(O,x,T.props.children,j,T.key):x!==null&&(x.elementType===X||typeof X=="object"&&X!==null&&X.$$typeof===ie&&mo(X)===x.type)?(x=l(x,T.props),ml(x,T),x.return=O,x):(x=hi(T.type,T.key,T.props,null,O.mode,j),ml(x,T),x.return=O,x)}function A(O,x,T,j){return x===null||x.tag!==4||x.stateNode.containerInfo!==T.containerInfo||x.stateNode.implementation!==T.implementation?(x=rs(T,O.mode,j),x.return=O,x):(x=l(x,T.children||[]),x.return=O,x)}function U(O,x,T,j,X){return x===null||x.tag!==7?(x=jn(T,O.mode,j,X),x.return=O,x):(x=l(x,T),x.return=O,x)}function L(O,x,T){if(typeof x=="string"&&x!==""||typeof x=="number"||typeof x=="bigint")return x=cs(""+x,O.mode,T),x.return=O,x;if(typeof x=="object"&&x!==null){switch(x.$$typeof){case w:return T=hi(x.type,x.key,x.props,null,O.mode,T),ml(T,x),T.return=O,T;case H:return x=rs(x,O.mode,T),x.return=O,x;case ie:var j=x._init;return x=j(x._payload),L(O,x,T)}if(Me(x)||Be(x))return x=jn(x,O.mode,T,null),x.return=O,x;if(typeof x.then=="function")return L(O,Di(x),T);if(x.$$typeof===F)return L(O,yi(O,x),T);zi(O,x)}return null}function N(O,x,T,j){var X=x!==null?x.key:null;if(typeof T=="string"&&T!==""||typeof T=="number"||typeof T=="bigint")return X!==null?null:g(O,x,""+T,j);if(typeof T=="object"&&T!==null){switch(T.$$typeof){case w:return T.key===X?b(O,x,T,j):null;case H:return T.key===X?A(O,x,T,j):null;case ie:return X=T._init,T=X(T._payload),N(O,x,T,j)}if(Me(T)||Be(T))return X!==null?null:U(O,x,T,j,null);if(typeof T.then=="function")return N(O,x,Di(T),j);if(T.$$typeof===F)return N(O,x,yi(O,T),j);zi(O,T)}return null}function R(O,x,T,j,X){if(typeof j=="string"&&j!==""||typeof j=="number"||typeof j=="bigint")return O=O.get(T)||null,g(x,O,""+j,X);if(typeof j=="object"&&j!==null){switch(j.$$typeof){case w:return O=O.get(j.key===null?T:j.key)||null,b(x,O,j,X);case H:return O=O.get(j.key===null?T:j.key)||null,A(x,O,j,X);case ie:var re=j._init;return j=re(j._payload),R(O,x,T,j,X)}if(Me(j)||Be(j))return O=O.get(T)||null,U(x,O,j,X,null);if(typeof j.then=="function")return R(O,x,T,Di(j),X);if(j.$$typeof===F)return R(O,x,T,yi(x,j),X);zi(x,j)}return null}function ee(O,x,T,j){for(var X=null,re=null,Z=x,P=x=0,Je=null;Z!==null&&P<T.length;P++){Z.index>P?(Je=Z,Z=null):Je=Z.sibling;var ve=N(O,Z,T[P],j);if(ve===null){Z===null&&(Z=Je);break}e&&Z&&ve.alternate===null&&t(O,Z),x=u(ve,x,P),re===null?X=ve:re.sibling=ve,re=ve,Z=Je}if(P===T.length)return n(O,Z),be&&Ln(O,P),X;if(Z===null){for(;P<T.length;P++)Z=L(O,T[P],j),Z!==null&&(x=u(Z,x,P),re===null?X=Z:re.sibling=Z,re=Z);return be&&Ln(O,P),X}for(Z=a(Z);P<T.length;P++)Je=R(Z,O,P,T[P],j),Je!==null&&(e&&Je.alternate!==null&&Z.delete(Je.key===null?P:Je.key),x=u(Je,x,P),re===null?X=Je:re.sibling=Je,re=Je);return e&&Z.forEach(function(Mn){return t(O,Mn)}),be&&Ln(O,P),X}function W(O,x,T,j){if(T==null)throw Error(s(151));for(var X=null,re=null,Z=x,P=x=0,Je=null,ve=T.next();Z!==null&&!ve.done;P++,ve=T.next()){Z.index>P?(Je=Z,Z=null):Je=Z.sibling;var Mn=N(O,Z,ve.value,j);if(Mn===null){Z===null&&(Z=Je);break}e&&Z&&Mn.alternate===null&&t(O,Z),x=u(Mn,x,P),re===null?X=Mn:re.sibling=Mn,re=Mn,Z=Je}if(ve.done)return n(O,Z),be&&Ln(O,P),X;if(Z===null){for(;!ve.done;P++,ve=T.next())ve=L(O,ve.value,j),ve!==null&&(x=u(ve,x,P),re===null?X=ve:re.sibling=ve,re=ve);return be&&Ln(O,P),X}for(Z=a(Z);!ve.done;P++,ve=T.next())ve=R(Z,O,P,ve.value,j),ve!==null&&(e&&ve.alternate!==null&&Z.delete(ve.key===null?P:ve.key),x=u(ve,x,P),re===null?X=ve:re.sibling=ve,re=ve);return e&&Z.forEach(function($0){return t(O,$0)}),be&&Ln(O,P),X}function Ae(O,x,T,j){if(typeof T=="object"&&T!==null&&T.type===M&&T.key===null&&(T=T.props.children),typeof T=="object"&&T!==null){switch(T.$$typeof){case w:e:{for(var X=T.key;x!==null;){if(x.key===X){if(X=T.type,X===M){if(x.tag===7){n(O,x.sibling),j=l(x,T.props.children),j.return=O,O=j;break e}}else if(x.elementType===X||typeof X=="object"&&X!==null&&X.$$typeof===ie&&mo(X)===x.type){n(O,x.sibling),j=l(x,T.props),ml(j,T),j.return=O,O=j;break e}n(O,x);break}else t(O,x);x=x.sibling}T.type===M?(j=jn(T.props.children,O.mode,j,T.key),j.return=O,O=j):(j=hi(T.type,T.key,T.props,null,O.mode,j),ml(j,T),j.return=O,O=j)}return d(O);case H:e:{for(X=T.key;x!==null;){if(x.key===X)if(x.tag===4&&x.stateNode.containerInfo===T.containerInfo&&x.stateNode.implementation===T.implementation){n(O,x.sibling),j=l(x,T.children||[]),j.return=O,O=j;break e}else{n(O,x);break}else t(O,x);x=x.sibling}j=rs(T,O.mode,j),j.return=O,O=j}return d(O);case ie:return X=T._init,T=X(T._payload),Ae(O,x,T,j)}if(Me(T))return ee(O,x,T,j);if(Be(T)){if(X=Be(T),typeof X!="function")throw Error(s(150));return T=X.call(T),W(O,x,T,j)}if(typeof T.then=="function")return Ae(O,x,Di(T),j);if(T.$$typeof===F)return Ae(O,x,yi(O,T),j);zi(O,T)}return typeof T=="string"&&T!==""||typeof T=="number"||typeof T=="bigint"?(T=""+T,x!==null&&x.tag===6?(n(O,x.sibling),j=l(x,T),j.return=O,O=j):(n(O,x),j=cs(T,O.mode,j),j.return=O,O=j),d(O)):n(O,x)}return function(O,x,T,j){try{gl=0;var X=Ae(O,x,T,j);return xa=null,X}catch(Z){if(Z===il||Z===bi)throw Z;var re=mt(29,Z,null,O.mode);return re.lanes=j,re.return=O,re}finally{}}}var Ea=po(!0),yo=po(!1),Rt=C(null),Lt=null;function hn(e){var t=e.alternate;V(ke,ke.current&1),V(Rt,e),Lt===null&&(t===null||ya.current!==null||t.memoizedState!==null)&&(Lt=e)}function vo(e){if(e.tag===22){if(V(ke,ke.current),V(Rt,e),Lt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Lt=e)}}else gn()}function gn(){V(ke,ke.current),V(Rt,Rt.current)}function Ft(e){G(Rt),Lt===e&&(Lt=null),G(ke)}var ke=C(0);function wi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||zc(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Gs(e,t,n,a){t=e.memoizedState,n=n(a,t),n=n==null?t:v({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Xs={enqueueSetState:function(e,t,n){e=e._reactInternals;var a=bt(),l=fn(a);l.payload=t,n!=null&&(l.callback=n),t=on(e,l,a),t!==null&&(St(t,e,a),sl(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var a=bt(),l=fn(a);l.tag=1,l.payload=t,n!=null&&(l.callback=n),t=on(e,l,a),t!==null&&(St(t,e,a),sl(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=bt(),a=fn(n);a.tag=2,t!=null&&(a.callback=t),t=on(e,a,n),t!==null&&(St(t,e,n),sl(t,e,n))}};function bo(e,t,n,a,l,u,d){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(a,u,d):t.prototype&&t.prototype.isPureReactComponent?!Wa(n,a)||!Wa(l,u):!0}function So(e,t,n,a){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,a),t.state!==e&&Xs.enqueueReplaceState(t,t.state,null)}function Xn(e,t){var n=t;if("ref"in t){n={};for(var a in t)a!=="ref"&&(n[a]=t[a])}if(e=e.defaultProps){n===t&&(n=v({},n));for(var l in e)n[l]===void 0&&(n[l]=e[l])}return n}var Ui=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function xo(e){Ui(e)}function Eo(e){console.error(e)}function Oo(e){Ui(e)}function ji(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(a){setTimeout(function(){throw a})}}function To(e,t,n){try{var a=e.onCaughtError;a(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(l){setTimeout(function(){throw l})}}function ks(e,t,n){return n=fn(n),n.tag=3,n.payload={element:null},n.callback=function(){ji(e,t)},n}function Ao(e){return e=fn(e),e.tag=3,e}function No(e,t,n,a){var l=n.type.getDerivedStateFromError;if(typeof l=="function"){var u=a.value;e.payload=function(){return l(u)},e.callback=function(){To(t,n,a)}}var d=n.stateNode;d!==null&&typeof d.componentDidCatch=="function"&&(e.callback=function(){To(t,n,a),typeof l!="function"&&(Sn===null?Sn=new Set([this]):Sn.add(this));var g=a.stack;this.componentDidCatch(a.value,{componentStack:g!==null?g:""})})}function $m(e,t,n,a,l){if(n.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(t=n.alternate,t!==null&&nl(t,n,l,!0),n=Rt.current,n!==null){switch(n.tag){case 13:return Lt===null?gc():n.alternate===null&&je===0&&(je=3),n.flags&=-257,n.flags|=65536,n.lanes=l,a===bs?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([a]):t.add(a),pc(e,a,l)),!1;case 22:return n.flags|=65536,a===bs?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([a]):n.add(a)),pc(e,a,l)),!1}throw Error(s(435,n.tag))}return pc(e,a,l),gc(),!1}if(be)return t=Rt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=l,a!==ds&&(e=Error(s(422),{cause:a}),tl(Ot(e,n)))):(a!==ds&&(t=Error(s(423),{cause:a}),tl(Ot(t,n))),e=e.current.alternate,e.flags|=65536,l&=-l,e.lanes|=l,a=Ot(a,n),l=ks(e.stateNode,a,l),Es(e,l),je!==4&&(je=2)),!1;var u=Error(s(520),{cause:a});if(u=Ot(u,n),El===null?El=[u]:El.push(u),je!==4&&(je=2),t===null)return!0;a=Ot(a,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=l&-l,n.lanes|=e,e=ks(n.stateNode,a,e),Es(n,e),!1;case 1:if(t=n.type,u=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(Sn===null||!Sn.has(u))))return n.flags|=65536,l&=-l,n.lanes|=l,l=Ao(l),No(l,e,n,a),Es(n,l),!1}n=n.return}while(n!==null);return!1}var Ro=Error(s(461)),Ze=!1;function $e(e,t,n,a){t.child=e===null?yo(t,null,n,a):Ea(t,e.child,n,a)}function Mo(e,t,n,a,l){n=n.render;var u=t.ref;if("ref"in a){var d={};for(var g in a)g!=="ref"&&(d[g]=a[g])}else d=a;return Yn(t),a=Rs(e,t,n,d,u,l),g=Ms(),e!==null&&!Ze?(_s(e,t,l),Wt(e,t,l)):(be&&g&&fs(t),t.flags|=1,$e(e,t,a,l),t.child)}function _o(e,t,n,a,l){if(e===null){var u=n.type;return typeof u=="function"&&!ss(u)&&u.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=u,Do(e,t,u,a,l)):(e=hi(n.type,null,a,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(u=e.child,!Ps(e,l)){var d=u.memoizedProps;if(n=n.compare,n=n!==null?n:Wa,n(d,a)&&e.ref===t.ref)return Wt(e,t,l)}return t.flags|=1,e=kt(u,a),e.ref=t.ref,e.return=t,t.child=e}function Do(e,t,n,a,l){if(e!==null){var u=e.memoizedProps;if(Wa(u,a)&&e.ref===t.ref)if(Ze=!1,t.pendingProps=a=u,Ps(e,l))(e.flags&131072)!==0&&(Ze=!0);else return t.lanes=e.lanes,Wt(e,t,l)}return Qs(e,t,n,a,l)}function zo(e,t,n){var a=t.pendingProps,l=a.children,u=e!==null?e.memoizedState:null;if(a.mode==="hidden"){if((t.flags&128)!==0){if(a=u!==null?u.baseLanes|n:n,e!==null){for(l=t.child=e.child,u=0;l!==null;)u=u|l.lanes|l.childLanes,l=l.sibling;t.childLanes=u&~a}else t.childLanes=0,t.child=null;return wo(e,t,a,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&vi(t,u!==null?u.cachePool:null),u!==null?_f(t,u):Ts(),vo(t);else return t.lanes=t.childLanes=536870912,wo(e,t,u!==null?u.baseLanes|n:n,n)}else u!==null?(vi(t,u.cachePool),_f(t,u),gn(),t.memoizedState=null):(e!==null&&vi(t,null),Ts(),gn());return $e(e,t,l,n),t.child}function wo(e,t,n,a){var l=vs();return l=l===null?null:{parent:Xe._currentValue,pool:l},t.memoizedState={baseLanes:n,cachePool:l},e!==null&&vi(t,null),Ts(),vo(t),e!==null&&nl(e,t,a,!0),null}function Ci(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(s(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function Qs(e,t,n,a,l){return Yn(t),n=Rs(e,t,n,a,void 0,l),a=Ms(),e!==null&&!Ze?(_s(e,t,l),Wt(e,t,l)):(be&&a&&fs(t),t.flags|=1,$e(e,t,n,l),t.child)}function Uo(e,t,n,a,l,u){return Yn(t),t.updateQueue=null,n=zf(t,a,n,l),Df(e),a=Ms(),e!==null&&!Ze?(_s(e,t,u),Wt(e,t,u)):(be&&a&&fs(t),t.flags|=1,$e(e,t,n,u),t.child)}function jo(e,t,n,a,l){if(Yn(t),t.stateNode===null){var u=da,d=n.contextType;typeof d=="object"&&d!==null&&(u=et(d)),u=new n(a,u),t.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=Xs,t.stateNode=u,u._reactInternals=t,u=t.stateNode,u.props=a,u.state=t.memoizedState,u.refs={},Ss(t),d=n.contextType,u.context=typeof d=="object"&&d!==null?et(d):da,u.state=t.memoizedState,d=n.getDerivedStateFromProps,typeof d=="function"&&(Gs(t,n,d,a),u.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(d=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),d!==u.state&&Xs.enqueueReplaceState(u,u.state,null),rl(t,a,u,l),cl(),u.state=t.memoizedState),typeof u.componentDidMount=="function"&&(t.flags|=4194308),a=!0}else if(e===null){u=t.stateNode;var g=t.memoizedProps,b=Xn(n,g);u.props=b;var A=u.context,U=n.contextType;d=da,typeof U=="object"&&U!==null&&(d=et(U));var L=n.getDerivedStateFromProps;U=typeof L=="function"||typeof u.getSnapshotBeforeUpdate=="function",g=t.pendingProps!==g,U||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(g||A!==d)&&So(t,u,a,d),rn=!1;var N=t.memoizedState;u.state=N,rl(t,a,u,l),cl(),A=t.memoizedState,g||N!==A||rn?(typeof L=="function"&&(Gs(t,n,L,a),A=t.memoizedState),(b=rn||bo(t,n,b,a,N,A,d))?(U||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(t.flags|=4194308)):(typeof u.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=A),u.props=a,u.state=A,u.context=d,a=b):(typeof u.componentDidMount=="function"&&(t.flags|=4194308),a=!1)}else{u=t.stateNode,xs(e,t),d=t.memoizedProps,U=Xn(n,d),u.props=U,L=t.pendingProps,N=u.context,A=n.contextType,b=da,typeof A=="object"&&A!==null&&(b=et(A)),g=n.getDerivedStateFromProps,(A=typeof g=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(d!==L||N!==b)&&So(t,u,a,b),rn=!1,N=t.memoizedState,u.state=N,rl(t,a,u,l),cl();var R=t.memoizedState;d!==L||N!==R||rn||e!==null&&e.dependencies!==null&&pi(e.dependencies)?(typeof g=="function"&&(Gs(t,n,g,a),R=t.memoizedState),(U=rn||bo(t,n,U,a,N,R,b)||e!==null&&e.dependencies!==null&&pi(e.dependencies))?(A||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(a,R,b),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(a,R,b)),typeof u.componentDidUpdate=="function"&&(t.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof u.componentDidUpdate!="function"||d===e.memoizedProps&&N===e.memoizedState||(t.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||d===e.memoizedProps&&N===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=R),u.props=a,u.state=R,u.context=b,a=U):(typeof u.componentDidUpdate!="function"||d===e.memoizedProps&&N===e.memoizedState||(t.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||d===e.memoizedProps&&N===e.memoizedState||(t.flags|=1024),a=!1)}return u=a,Ci(e,t),a=(t.flags&128)!==0,u||a?(u=t.stateNode,n=a&&typeof n.getDerivedStateFromError!="function"?null:u.render(),t.flags|=1,e!==null&&a?(t.child=Ea(t,e.child,null,l),t.child=Ea(t,null,n,l)):$e(e,t,n,l),t.memoizedState=u.state,e=t.child):e=Wt(e,t,l),e}function Co(e,t,n,a){return el(),t.flags|=256,$e(e,t,n,a),t.child}var Zs={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Ks(e){return{baseLanes:e,cachePool:xf()}}function Js(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=Mt),e}function Lo(e,t,n){var a=t.pendingProps,l=!1,u=(t.flags&128)!==0,d;if((d=u)||(d=e!==null&&e.memoizedState===null?!1:(ke.current&2)!==0),d&&(l=!0,t.flags&=-129),d=(t.flags&32)!==0,t.flags&=-33,e===null){if(be){if(l?hn(t):gn(),be){var g=Ue,b;if(b=g){e:{for(b=g,g=Ct;b.nodeType!==8;){if(!g){g=null;break e}if(b=wt(b.nextSibling),b===null){g=null;break e}}g=b}g!==null?(t.memoizedState={dehydrated:g,treeContext:Cn!==null?{id:Qt,overflow:Zt}:null,retryLane:536870912,hydrationErrors:null},b=mt(18,null,null,0),b.stateNode=g,b.return=t,t.child=b,at=t,Ue=null,b=!0):b=!1}b||Bn(t)}if(g=t.memoizedState,g!==null&&(g=g.dehydrated,g!==null))return zc(g)?t.lanes=32:t.lanes=536870912,null;Ft(t)}return g=a.children,a=a.fallback,l?(gn(),l=t.mode,g=Li({mode:"hidden",children:g},l),a=jn(a,l,n,null),g.return=t,a.return=t,g.sibling=a,t.child=g,l=t.child,l.memoizedState=Ks(n),l.childLanes=Js(e,d,n),t.memoizedState=Zs,a):(hn(t),$s(t,g))}if(b=e.memoizedState,b!==null&&(g=b.dehydrated,g!==null)){if(u)t.flags&256?(hn(t),t.flags&=-257,t=Fs(e,t,n)):t.memoizedState!==null?(gn(),t.child=e.child,t.flags|=128,t=null):(gn(),l=a.fallback,g=t.mode,a=Li({mode:"visible",children:a.children},g),l=jn(l,g,n,null),l.flags|=2,a.return=t,l.return=t,a.sibling=l,t.child=a,Ea(t,e.child,null,n),a=t.child,a.memoizedState=Ks(n),a.childLanes=Js(e,d,n),t.memoizedState=Zs,t=l);else if(hn(t),zc(g)){if(d=g.nextSibling&&g.nextSibling.dataset,d)var A=d.dgst;d=A,a=Error(s(419)),a.stack="",a.digest=d,tl({value:a,source:null,stack:null}),t=Fs(e,t,n)}else if(Ze||nl(e,t,n,!1),d=(n&e.childLanes)!==0,Ze||d){if(d=Re,d!==null&&(a=n&-n,a=(a&42)!==0?1:wu(a),a=(a&(d.suspendedLanes|n))!==0?0:a,a!==0&&a!==b.retryLane))throw b.retryLane=a,oa(e,a),St(d,e,a),Ro;g.data==="$?"||gc(),t=Fs(e,t,n)}else g.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=b.treeContext,Ue=wt(g.nextSibling),at=t,be=!0,Hn=null,Ct=!1,e!==null&&(At[Nt++]=Qt,At[Nt++]=Zt,At[Nt++]=Cn,Qt=e.id,Zt=e.overflow,Cn=t),t=$s(t,a.children),t.flags|=4096);return t}return l?(gn(),l=a.fallback,g=t.mode,b=e.child,A=b.sibling,a=kt(b,{mode:"hidden",children:a.children}),a.subtreeFlags=b.subtreeFlags&65011712,A!==null?l=kt(A,l):(l=jn(l,g,n,null),l.flags|=2),l.return=t,a.return=t,a.sibling=l,t.child=a,a=l,l=t.child,g=e.child.memoizedState,g===null?g=Ks(n):(b=g.cachePool,b!==null?(A=Xe._currentValue,b=b.parent!==A?{parent:A,pool:A}:b):b=xf(),g={baseLanes:g.baseLanes|n,cachePool:b}),l.memoizedState=g,l.childLanes=Js(e,d,n),t.memoizedState=Zs,a):(hn(t),n=e.child,e=n.sibling,n=kt(n,{mode:"visible",children:a.children}),n.return=t,n.sibling=null,e!==null&&(d=t.deletions,d===null?(t.deletions=[e],t.flags|=16):d.push(e)),t.child=n,t.memoizedState=null,n)}function $s(e,t){return t=Li({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Li(e,t){return e=mt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Fs(e,t,n){return Ea(t,e.child,null,n),e=$s(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Ho(e,t,n){e.lanes|=t;var a=e.alternate;a!==null&&(a.lanes|=t),gs(e.return,t,n)}function Ws(e,t,n,a,l){var u=e.memoizedState;u===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:n,tailMode:l}:(u.isBackwards=t,u.rendering=null,u.renderingStartTime=0,u.last=a,u.tail=n,u.tailMode=l)}function Bo(e,t,n){var a=t.pendingProps,l=a.revealOrder,u=a.tail;if($e(e,t,a.children,n),a=ke.current,(a&2)!==0)a=a&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Ho(e,n,t);else if(e.tag===19)Ho(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}switch(V(ke,a),l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&wi(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),Ws(t,!1,l,n,u);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&wi(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}Ws(t,!0,n,null,u);break;case"together":Ws(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Wt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),bn|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(nl(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(s(153));if(t.child!==null){for(e=t.child,n=kt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=kt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Ps(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&pi(e)))}function Fm(e,t,n){switch(t.tag){case 3:_e(t,t.stateNode.containerInfo),cn(t,Xe,e.memoizedState.cache),el();break;case 27:case 5:Ru(t);break;case 4:_e(t,t.stateNode.containerInfo);break;case 10:cn(t,t.type,t.memoizedProps.value);break;case 13:var a=t.memoizedState;if(a!==null)return a.dehydrated!==null?(hn(t),t.flags|=128,null):(n&t.child.childLanes)!==0?Lo(e,t,n):(hn(t),e=Wt(e,t,n),e!==null?e.sibling:null);hn(t);break;case 19:var l=(e.flags&128)!==0;if(a=(n&t.childLanes)!==0,a||(nl(e,t,n,!1),a=(n&t.childLanes)!==0),l){if(a)return Bo(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),V(ke,ke.current),a)break;return null;case 22:case 23:return t.lanes=0,zo(e,t,n);case 24:cn(t,Xe,e.memoizedState.cache)}return Wt(e,t,n)}function qo(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)Ze=!0;else{if(!Ps(e,n)&&(t.flags&128)===0)return Ze=!1,Fm(e,t,n);Ze=(e.flags&131072)!==0}else Ze=!1,be&&(t.flags&1048576)!==0&&gf(t,mi,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var a=t.elementType,l=a._init;if(a=l(a._payload),t.type=a,typeof a=="function")ss(a)?(e=Xn(a,e),t.tag=1,t=jo(null,t,a,e,n)):(t.tag=0,t=Qs(null,t,a,e,n));else{if(a!=null){if(l=a.$$typeof,l===le){t.tag=11,t=Mo(null,t,a,e,n);break e}else if(l===fe){t.tag=14,t=_o(null,t,a,e,n);break e}}throw t=K(a)||a,Error(s(306,t,""))}}return t;case 0:return Qs(e,t,t.type,t.pendingProps,n);case 1:return a=t.type,l=Xn(a,t.pendingProps),jo(e,t,a,l,n);case 3:e:{if(_e(t,t.stateNode.containerInfo),e===null)throw Error(s(387));a=t.pendingProps;var u=t.memoizedState;l=u.element,xs(e,t),rl(t,a,null,n);var d=t.memoizedState;if(a=d.cache,cn(t,Xe,a),a!==u.cache&&ms(t,[Xe],n,!0),cl(),a=d.element,u.isDehydrated)if(u={element:a,isDehydrated:!1,cache:d.cache},t.updateQueue.baseState=u,t.memoizedState=u,t.flags&256){t=Co(e,t,a,n);break e}else if(a!==l){l=Ot(Error(s(424)),t),tl(l),t=Co(e,t,a,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Ue=wt(e.firstChild),at=t,be=!0,Hn=null,Ct=!0,n=yo(t,null,a,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(el(),a===l){t=Wt(e,t,n);break e}$e(e,t,a,n)}t=t.child}return t;case 26:return Ci(e,t),e===null?(n=Xd(t.type,null,t.pendingProps,null))?t.memoizedState=n:be||(n=t.type,e=t.pendingProps,a=Fi(ne.current).createElement(n),a[Ie]=t,a[lt]=e,We(a,n,e),Qe(a),t.stateNode=a):t.memoizedState=Xd(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Ru(t),e===null&&be&&(a=t.stateNode=Yd(t.type,t.pendingProps,ne.current),at=t,Ct=!0,l=Ue,On(t.type)?(wc=l,Ue=wt(a.firstChild)):Ue=l),$e(e,t,t.pendingProps.children,n),Ci(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&be&&((l=a=Ue)&&(a=T0(a,t.type,t.pendingProps,Ct),a!==null?(t.stateNode=a,at=t,Ue=wt(a.firstChild),Ct=!1,l=!0):l=!1),l||Bn(t)),Ru(t),l=t.type,u=t.pendingProps,d=e!==null?e.memoizedProps:null,a=u.children,Mc(l,u)?a=null:d!==null&&Mc(l,d)&&(t.flags|=32),t.memoizedState!==null&&(l=Rs(e,t,Gm,null,null,n),zl._currentValue=l),Ci(e,t),$e(e,t,a,n),t.child;case 6:return e===null&&be&&((e=n=Ue)&&(n=A0(n,t.pendingProps,Ct),n!==null?(t.stateNode=n,at=t,Ue=null,e=!0):e=!1),e||Bn(t)),null;case 13:return Lo(e,t,n);case 4:return _e(t,t.stateNode.containerInfo),a=t.pendingProps,e===null?t.child=Ea(t,null,a,n):$e(e,t,a,n),t.child;case 11:return Mo(e,t,t.type,t.pendingProps,n);case 7:return $e(e,t,t.pendingProps,n),t.child;case 8:return $e(e,t,t.pendingProps.children,n),t.child;case 12:return $e(e,t,t.pendingProps.children,n),t.child;case 10:return a=t.pendingProps,cn(t,t.type,a.value),$e(e,t,a.children,n),t.child;case 9:return l=t.type._context,a=t.pendingProps.children,Yn(t),l=et(l),a=a(l),t.flags|=1,$e(e,t,a,n),t.child;case 14:return _o(e,t,t.type,t.pendingProps,n);case 15:return Do(e,t,t.type,t.pendingProps,n);case 19:return Bo(e,t,n);case 31:return a=t.pendingProps,n=t.mode,a={mode:a.mode,children:a.children},e===null?(n=Li(a,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=kt(e.child,a),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return zo(e,t,n);case 24:return Yn(t),a=et(Xe),e===null?(l=vs(),l===null&&(l=Re,u=ps(),l.pooledCache=u,u.refCount++,u!==null&&(l.pooledCacheLanes|=n),l=u),t.memoizedState={parent:a,cache:l},Ss(t),cn(t,Xe,l)):((e.lanes&n)!==0&&(xs(e,t),rl(t,null,null,n),cl()),l=e.memoizedState,u=t.memoizedState,l.parent!==a?(l={parent:a,cache:a},t.memoizedState=l,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=l),cn(t,Xe,a)):(a=u.cache,cn(t,Xe,a),a!==l.cache&&ms(t,[Xe],n,!0))),$e(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(s(156,t.tag))}function Pt(e){e.flags|=4}function Yo(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Jd(t)){if(t=Rt.current,t!==null&&((me&4194048)===me?Lt!==null:(me&62914560)!==me&&(me&536870912)===0||t!==Lt))throw ul=bs,Ef;e.flags|=8192}}function Hi(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?pr():536870912,e.lanes|=t,Na|=t)}function pl(e,t){if(!be)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var a=null;n!==null;)n.alternate!==null&&(a=n),n=n.sibling;a===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:a.sibling=null}}function we(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,a=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,a|=l.subtreeFlags&65011712,a|=l.flags&65011712,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,a|=l.subtreeFlags,a|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=a,e.childLanes=n,t}function Wm(e,t,n){var a=t.pendingProps;switch(os(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return we(t),null;case 1:return we(t),null;case 3:return n=t.stateNode,a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),Jt(Xe),an(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(Ia(t)?Pt(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,yf())),we(t),null;case 26:return n=t.memoizedState,e===null?(Pt(t),n!==null?(we(t),Yo(t,n)):(we(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Pt(t),we(t),Yo(t,n)):(we(t),t.flags&=-16777217):(e.memoizedProps!==a&&Pt(t),we(t),t.flags&=-16777217),null;case 27:Jl(t),n=ne.current;var l=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==a&&Pt(t);else{if(!a){if(t.stateNode===null)throw Error(s(166));return we(t),null}e=J.current,Ia(t)?mf(t):(e=Yd(l,a,n),t.stateNode=e,Pt(t))}return we(t),null;case 5:if(Jl(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==a&&Pt(t);else{if(!a){if(t.stateNode===null)throw Error(s(166));return we(t),null}if(e=J.current,Ia(t))mf(t);else{switch(l=Fi(ne.current),e){case 1:e=l.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=l.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=l.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=l.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=l.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof a.is=="string"?l.createElement("select",{is:a.is}):l.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e=typeof a.is=="string"?l.createElement(n,{is:a.is}):l.createElement(n)}}e[Ie]=t,e[lt]=a;e:for(l=t.child;l!==null;){if(l.tag===5||l.tag===6)e.appendChild(l.stateNode);else if(l.tag!==4&&l.tag!==27&&l.child!==null){l.child.return=l,l=l.child;continue}if(l===t)break e;for(;l.sibling===null;){if(l.return===null||l.return===t)break e;l=l.return}l.sibling.return=l.return,l=l.sibling}t.stateNode=e;e:switch(We(e,n,a),n){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Pt(t)}}return we(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==a&&Pt(t);else{if(typeof a!="string"&&t.stateNode===null)throw Error(s(166));if(e=ne.current,Ia(t)){if(e=t.stateNode,n=t.memoizedProps,a=null,l=at,l!==null)switch(l.tag){case 27:case 5:a=l.memoizedProps}e[Ie]=t,e=!!(e.nodeValue===n||a!==null&&a.suppressHydrationWarning===!0||Ud(e.nodeValue,n)),e||Bn(t)}else e=Fi(e).createTextNode(a),e[Ie]=t,t.stateNode=e}return we(t),null;case 13:if(a=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(l=Ia(t),a!==null&&a.dehydrated!==null){if(e===null){if(!l)throw Error(s(318));if(l=t.memoizedState,l=l!==null?l.dehydrated:null,!l)throw Error(s(317));l[Ie]=t}else el(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;we(t),l=!1}else l=yf(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=l),l=!0;if(!l)return t.flags&256?(Ft(t),t):(Ft(t),null)}if(Ft(t),(t.flags&128)!==0)return t.lanes=n,t;if(n=a!==null,e=e!==null&&e.memoizedState!==null,n){a=t.child,l=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(l=a.alternate.memoizedState.cachePool.pool);var u=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(u=a.memoizedState.cachePool.pool),u!==l&&(a.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),Hi(t,t.updateQueue),we(t),null;case 4:return an(),e===null&&Oc(t.stateNode.containerInfo),we(t),null;case 10:return Jt(t.type),we(t),null;case 19:if(G(ke),l=t.memoizedState,l===null)return we(t),null;if(a=(t.flags&128)!==0,u=l.rendering,u===null)if(a)pl(l,!1);else{if(je!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(u=wi(e),u!==null){for(t.flags|=128,pl(l,!1),e=u.updateQueue,t.updateQueue=e,Hi(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)hf(n,e),n=n.sibling;return V(ke,ke.current&1|2),t.child}e=e.sibling}l.tail!==null&&jt()>Yi&&(t.flags|=128,a=!0,pl(l,!1),t.lanes=4194304)}else{if(!a)if(e=wi(u),e!==null){if(t.flags|=128,a=!0,e=e.updateQueue,t.updateQueue=e,Hi(t,e),pl(l,!0),l.tail===null&&l.tailMode==="hidden"&&!u.alternate&&!be)return we(t),null}else 2*jt()-l.renderingStartTime>Yi&&n!==536870912&&(t.flags|=128,a=!0,pl(l,!1),t.lanes=4194304);l.isBackwards?(u.sibling=t.child,t.child=u):(e=l.last,e!==null?e.sibling=u:t.child=u,l.last=u)}return l.tail!==null?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=jt(),t.sibling=null,e=ke.current,V(ke,a?e&1|2:e&1),t):(we(t),null);case 22:case 23:return Ft(t),As(),a=t.memoizedState!==null,e!==null?e.memoizedState!==null!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?(n&536870912)!==0&&(t.flags&128)===0&&(we(t),t.subtreeFlags&6&&(t.flags|=8192)):we(t),n=t.updateQueue,n!==null&&Hi(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),a=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),a!==n&&(t.flags|=2048),e!==null&&G(Vn),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),Jt(Xe),we(t),null;case 25:return null;case 30:return null}throw Error(s(156,t.tag))}function Pm(e,t){switch(os(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Jt(Xe),an(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return Jl(t),null;case 13:if(Ft(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(s(340));el()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return G(ke),null;case 4:return an(),null;case 10:return Jt(t.type),null;case 22:case 23:return Ft(t),As(),e!==null&&G(Vn),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Jt(Xe),null;case 25:return null;default:return null}}function Vo(e,t){switch(os(t),t.tag){case 3:Jt(Xe),an();break;case 26:case 27:case 5:Jl(t);break;case 4:an();break;case 13:Ft(t);break;case 19:G(ke);break;case 10:Jt(t.type);break;case 22:case 23:Ft(t),As(),e!==null&&G(Vn);break;case 24:Jt(Xe)}}function yl(e,t){try{var n=t.updateQueue,a=n!==null?n.lastEffect:null;if(a!==null){var l=a.next;n=l;do{if((n.tag&e)===e){a=void 0;var u=n.create,d=n.inst;a=u(),d.destroy=a}n=n.next}while(n!==l)}}catch(g){Ne(t,t.return,g)}}function mn(e,t,n){try{var a=t.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var u=l.next;a=u;do{if((a.tag&e)===e){var d=a.inst,g=d.destroy;if(g!==void 0){d.destroy=void 0,l=t;var b=n,A=g;try{A()}catch(U){Ne(l,b,U)}}}a=a.next}while(a!==u)}}catch(U){Ne(t,t.return,U)}}function Go(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{Mf(t,n)}catch(a){Ne(e,e.return,a)}}}function Xo(e,t,n){n.props=Xn(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(a){Ne(e,t,a)}}function vl(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var a=e.stateNode;break;case 30:a=e.stateNode;break;default:a=e.stateNode}typeof n=="function"?e.refCleanup=n(a):n.current=a}}catch(l){Ne(e,t,l)}}function Ht(e,t){var n=e.ref,a=e.refCleanup;if(n!==null)if(typeof a=="function")try{a()}catch(l){Ne(e,t,l)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(l){Ne(e,t,l)}else n.current=null}function ko(e){var t=e.type,n=e.memoizedProps,a=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&a.focus();break e;case"img":n.src?a.src=n.src:n.srcSet&&(a.srcset=n.srcSet)}}catch(l){Ne(e,e.return,l)}}function Is(e,t,n){try{var a=e.stateNode;b0(a,e.type,n,t),a[lt]=t}catch(l){Ne(e,e.return,l)}}function Qo(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&On(e.type)||e.tag===4}function ec(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Qo(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&On(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function tc(e,t,n){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=$i));else if(a!==4&&(a===27&&On(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(tc(e,t,n),e=e.sibling;e!==null;)tc(e,t,n),e=e.sibling}function Bi(e,t,n){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(a!==4&&(a===27&&On(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(Bi(e,t,n),e=e.sibling;e!==null;)Bi(e,t,n),e=e.sibling}function Zo(e){var t=e.stateNode,n=e.memoizedProps;try{for(var a=e.type,l=t.attributes;l.length;)t.removeAttributeNode(l[0]);We(t,a,n),t[Ie]=e,t[lt]=n}catch(u){Ne(e,e.return,u)}}var It=!1,Le=!1,nc=!1,Ko=typeof WeakSet=="function"?WeakSet:Set,Ke=null;function Im(e,t){if(e=e.containerInfo,Nc=nu,e=nf(e),es(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var a=n.getSelection&&n.getSelection();if(a&&a.rangeCount!==0){n=a.anchorNode;var l=a.anchorOffset,u=a.focusNode;a=a.focusOffset;try{n.nodeType,u.nodeType}catch{n=null;break e}var d=0,g=-1,b=-1,A=0,U=0,L=e,N=null;t:for(;;){for(var R;L!==n||l!==0&&L.nodeType!==3||(g=d+l),L!==u||a!==0&&L.nodeType!==3||(b=d+a),L.nodeType===3&&(d+=L.nodeValue.length),(R=L.firstChild)!==null;)N=L,L=R;for(;;){if(L===e)break t;if(N===n&&++A===l&&(g=d),N===u&&++U===a&&(b=d),(R=L.nextSibling)!==null)break;L=N,N=L.parentNode}L=R}n=g===-1||b===-1?null:{start:g,end:b}}else n=null}n=n||{start:0,end:0}}else n=null;for(Rc={focusedElem:e,selectionRange:n},nu=!1,Ke=t;Ke!==null;)if(t=Ke,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Ke=e;else for(;Ke!==null;){switch(t=Ke,u=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&u!==null){e=void 0,n=t,l=u.memoizedProps,u=u.memoizedState,a=n.stateNode;try{var ee=Xn(n.type,l,n.elementType===n.type);e=a.getSnapshotBeforeUpdate(ee,u),a.__reactInternalSnapshotBeforeUpdate=e}catch(W){Ne(n,n.return,W)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)Dc(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Dc(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(s(163))}if(e=t.sibling,e!==null){e.return=t.return,Ke=e;break}Ke=t.return}}function Jo(e,t,n){var a=n.flags;switch(n.tag){case 0:case 11:case 15:pn(e,n),a&4&&yl(5,n);break;case 1:if(pn(e,n),a&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(d){Ne(n,n.return,d)}else{var l=Xn(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(l,t,e.__reactInternalSnapshotBeforeUpdate)}catch(d){Ne(n,n.return,d)}}a&64&&Go(n),a&512&&vl(n,n.return);break;case 3:if(pn(e,n),a&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{Mf(e,t)}catch(d){Ne(n,n.return,d)}}break;case 27:t===null&&a&4&&Zo(n);case 26:case 5:pn(e,n),t===null&&a&4&&ko(n),a&512&&vl(n,n.return);break;case 12:pn(e,n);break;case 13:pn(e,n),a&4&&Wo(e,n),a&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=c0.bind(null,n),N0(e,n))));break;case 22:if(a=n.memoizedState!==null||It,!a){t=t!==null&&t.memoizedState!==null||Le,l=It;var u=Le;It=a,(Le=t)&&!u?yn(e,n,(n.subtreeFlags&8772)!==0):pn(e,n),It=l,Le=u}break;case 30:break;default:pn(e,n)}}function $o(e){var t=e.alternate;t!==null&&(e.alternate=null,$o(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Cu(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var De=null,st=!1;function en(e,t,n){for(n=n.child;n!==null;)Fo(e,t,n),n=n.sibling}function Fo(e,t,n){if(dt&&typeof dt.onCommitFiberUnmount=="function")try{dt.onCommitFiberUnmount(qa,n)}catch{}switch(n.tag){case 26:Le||Ht(n,t),en(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Le||Ht(n,t);var a=De,l=st;On(n.type)&&(De=n.stateNode,st=!1),en(e,t,n),Rl(n.stateNode),De=a,st=l;break;case 5:Le||Ht(n,t);case 6:if(a=De,l=st,De=null,en(e,t,n),De=a,st=l,De!==null)if(st)try{(De.nodeType===9?De.body:De.nodeName==="HTML"?De.ownerDocument.body:De).removeChild(n.stateNode)}catch(u){Ne(n,t,u)}else try{De.removeChild(n.stateNode)}catch(u){Ne(n,t,u)}break;case 18:De!==null&&(st?(e=De,Bd(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),Cl(e)):Bd(De,n.stateNode));break;case 4:a=De,l=st,De=n.stateNode.containerInfo,st=!0,en(e,t,n),De=a,st=l;break;case 0:case 11:case 14:case 15:Le||mn(2,n,t),Le||mn(4,n,t),en(e,t,n);break;case 1:Le||(Ht(n,t),a=n.stateNode,typeof a.componentWillUnmount=="function"&&Xo(n,t,a)),en(e,t,n);break;case 21:en(e,t,n);break;case 22:Le=(a=Le)||n.memoizedState!==null,en(e,t,n),Le=a;break;default:en(e,t,n)}}function Wo(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Cl(e)}catch(n){Ne(t,t.return,n)}}function e0(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Ko),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Ko),t;default:throw Error(s(435,e.tag))}}function ac(e,t){var n=e0(e);t.forEach(function(a){var l=r0.bind(null,e,a);n.has(a)||(n.add(a),a.then(l,l))})}function pt(e,t){var n=t.deletions;if(n!==null)for(var a=0;a<n.length;a++){var l=n[a],u=e,d=t,g=d;e:for(;g!==null;){switch(g.tag){case 27:if(On(g.type)){De=g.stateNode,st=!1;break e}break;case 5:De=g.stateNode,st=!1;break e;case 3:case 4:De=g.stateNode.containerInfo,st=!0;break e}g=g.return}if(De===null)throw Error(s(160));Fo(u,d,l),De=null,st=!1,u=l.alternate,u!==null&&(u.return=null),l.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Po(t,e),t=t.sibling}var zt=null;function Po(e,t){var n=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:pt(t,e),yt(e),a&4&&(mn(3,e,e.return),yl(3,e),mn(5,e,e.return));break;case 1:pt(t,e),yt(e),a&512&&(Le||n===null||Ht(n,n.return)),a&64&&It&&(e=e.updateQueue,e!==null&&(a=e.callbacks,a!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?a:n.concat(a))));break;case 26:var l=zt;if(pt(t,e),yt(e),a&512&&(Le||n===null||Ht(n,n.return)),a&4){var u=n!==null?n.memoizedState:null;if(a=e.memoizedState,n===null)if(a===null)if(e.stateNode===null){e:{a=e.type,n=e.memoizedProps,l=l.ownerDocument||l;t:switch(a){case"title":u=l.getElementsByTagName("title")[0],(!u||u[Ga]||u[Ie]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=l.createElement(a),l.head.insertBefore(u,l.querySelector("head > title"))),We(u,a,n),u[Ie]=e,Qe(u),a=u;break e;case"link":var d=Zd("link","href",l).get(a+(n.href||""));if(d){for(var g=0;g<d.length;g++)if(u=d[g],u.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&u.getAttribute("rel")===(n.rel==null?null:n.rel)&&u.getAttribute("title")===(n.title==null?null:n.title)&&u.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){d.splice(g,1);break t}}u=l.createElement(a),We(u,a,n),l.head.appendChild(u);break;case"meta":if(d=Zd("meta","content",l).get(a+(n.content||""))){for(g=0;g<d.length;g++)if(u=d[g],u.getAttribute("content")===(n.content==null?null:""+n.content)&&u.getAttribute("name")===(n.name==null?null:n.name)&&u.getAttribute("property")===(n.property==null?null:n.property)&&u.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&u.getAttribute("charset")===(n.charSet==null?null:n.charSet)){d.splice(g,1);break t}}u=l.createElement(a),We(u,a,n),l.head.appendChild(u);break;default:throw Error(s(468,a))}u[Ie]=e,Qe(u),a=u}e.stateNode=a}else Kd(l,e.type,e.stateNode);else e.stateNode=Qd(l,a,e.memoizedProps);else u!==a?(u===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):u.count--,a===null?Kd(l,e.type,e.stateNode):Qd(l,a,e.memoizedProps)):a===null&&e.stateNode!==null&&Is(e,e.memoizedProps,n.memoizedProps)}break;case 27:pt(t,e),yt(e),a&512&&(Le||n===null||Ht(n,n.return)),n!==null&&a&4&&Is(e,e.memoizedProps,n.memoizedProps);break;case 5:if(pt(t,e),yt(e),a&512&&(Le||n===null||Ht(n,n.return)),e.flags&32){l=e.stateNode;try{la(l,"")}catch(R){Ne(e,e.return,R)}}a&4&&e.stateNode!=null&&(l=e.memoizedProps,Is(e,l,n!==null?n.memoizedProps:l)),a&1024&&(nc=!0);break;case 6:if(pt(t,e),yt(e),a&4){if(e.stateNode===null)throw Error(s(162));a=e.memoizedProps,n=e.stateNode;try{n.nodeValue=a}catch(R){Ne(e,e.return,R)}}break;case 3:if(Ii=null,l=zt,zt=Wi(t.containerInfo),pt(t,e),zt=l,yt(e),a&4&&n!==null&&n.memoizedState.isDehydrated)try{Cl(t.containerInfo)}catch(R){Ne(e,e.return,R)}nc&&(nc=!1,Io(e));break;case 4:a=zt,zt=Wi(e.stateNode.containerInfo),pt(t,e),yt(e),zt=a;break;case 12:pt(t,e),yt(e);break;case 13:pt(t,e),yt(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(rc=jt()),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,ac(e,a)));break;case 22:l=e.memoizedState!==null;var b=n!==null&&n.memoizedState!==null,A=It,U=Le;if(It=A||l,Le=U||b,pt(t,e),Le=U,It=A,yt(e),a&8192)e:for(t=e.stateNode,t._visibility=l?t._visibility&-2:t._visibility|1,l&&(n===null||b||It||Le||kn(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){b=n=t;try{if(u=b.stateNode,l)d=u.style,typeof d.setProperty=="function"?d.setProperty("display","none","important"):d.display="none";else{g=b.stateNode;var L=b.memoizedProps.style,N=L!=null&&L.hasOwnProperty("display")?L.display:null;g.style.display=N==null||typeof N=="boolean"?"":(""+N).trim()}}catch(R){Ne(b,b.return,R)}}}else if(t.tag===6){if(n===null){b=t;try{b.stateNode.nodeValue=l?"":b.memoizedProps}catch(R){Ne(b,b.return,R)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}a&4&&(a=e.updateQueue,a!==null&&(n=a.retryQueue,n!==null&&(a.retryQueue=null,ac(e,n))));break;case 19:pt(t,e),yt(e),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,ac(e,a)));break;case 30:break;case 21:break;default:pt(t,e),yt(e)}}function yt(e){var t=e.flags;if(t&2){try{for(var n,a=e.return;a!==null;){if(Qo(a)){n=a;break}a=a.return}if(n==null)throw Error(s(160));switch(n.tag){case 27:var l=n.stateNode,u=ec(e);Bi(e,u,l);break;case 5:var d=n.stateNode;n.flags&32&&(la(d,""),n.flags&=-33);var g=ec(e);Bi(e,g,d);break;case 3:case 4:var b=n.stateNode.containerInfo,A=ec(e);tc(e,A,b);break;default:throw Error(s(161))}}catch(U){Ne(e,e.return,U)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Io(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Io(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function pn(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Jo(e,t.alternate,t),t=t.sibling}function kn(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:mn(4,t,t.return),kn(t);break;case 1:Ht(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&Xo(t,t.return,n),kn(t);break;case 27:Rl(t.stateNode);case 26:case 5:Ht(t,t.return),kn(t);break;case 22:t.memoizedState===null&&kn(t);break;case 30:kn(t);break;default:kn(t)}e=e.sibling}}function yn(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var a=t.alternate,l=e,u=t,d=u.flags;switch(u.tag){case 0:case 11:case 15:yn(l,u,n),yl(4,u);break;case 1:if(yn(l,u,n),a=u,l=a.stateNode,typeof l.componentDidMount=="function")try{l.componentDidMount()}catch(A){Ne(a,a.return,A)}if(a=u,l=a.updateQueue,l!==null){var g=a.stateNode;try{var b=l.shared.hiddenCallbacks;if(b!==null)for(l.shared.hiddenCallbacks=null,l=0;l<b.length;l++)Rf(b[l],g)}catch(A){Ne(a,a.return,A)}}n&&d&64&&Go(u),vl(u,u.return);break;case 27:Zo(u);case 26:case 5:yn(l,u,n),n&&a===null&&d&4&&ko(u),vl(u,u.return);break;case 12:yn(l,u,n);break;case 13:yn(l,u,n),n&&d&4&&Wo(l,u);break;case 22:u.memoizedState===null&&yn(l,u,n),vl(u,u.return);break;case 30:break;default:yn(l,u,n)}t=t.sibling}}function lc(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&al(n))}function ic(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&al(e))}function Bt(e,t,n,a){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)ed(e,t,n,a),t=t.sibling}function ed(e,t,n,a){var l=t.flags;switch(t.tag){case 0:case 11:case 15:Bt(e,t,n,a),l&2048&&yl(9,t);break;case 1:Bt(e,t,n,a);break;case 3:Bt(e,t,n,a),l&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&al(e)));break;case 12:if(l&2048){Bt(e,t,n,a),e=t.stateNode;try{var u=t.memoizedProps,d=u.id,g=u.onPostCommit;typeof g=="function"&&g(d,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(b){Ne(t,t.return,b)}}else Bt(e,t,n,a);break;case 13:Bt(e,t,n,a);break;case 23:break;case 22:u=t.stateNode,d=t.alternate,t.memoizedState!==null?u._visibility&2?Bt(e,t,n,a):bl(e,t):u._visibility&2?Bt(e,t,n,a):(u._visibility|=2,Oa(e,t,n,a,(t.subtreeFlags&10256)!==0)),l&2048&&lc(d,t);break;case 24:Bt(e,t,n,a),l&2048&&ic(t.alternate,t);break;default:Bt(e,t,n,a)}}function Oa(e,t,n,a,l){for(l=l&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var u=e,d=t,g=n,b=a,A=d.flags;switch(d.tag){case 0:case 11:case 15:Oa(u,d,g,b,l),yl(8,d);break;case 23:break;case 22:var U=d.stateNode;d.memoizedState!==null?U._visibility&2?Oa(u,d,g,b,l):bl(u,d):(U._visibility|=2,Oa(u,d,g,b,l)),l&&A&2048&&lc(d.alternate,d);break;case 24:Oa(u,d,g,b,l),l&&A&2048&&ic(d.alternate,d);break;default:Oa(u,d,g,b,l)}t=t.sibling}}function bl(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,a=t,l=a.flags;switch(a.tag){case 22:bl(n,a),l&2048&&lc(a.alternate,a);break;case 24:bl(n,a),l&2048&&ic(a.alternate,a);break;default:bl(n,a)}t=t.sibling}}var Sl=8192;function Ta(e){if(e.subtreeFlags&Sl)for(e=e.child;e!==null;)td(e),e=e.sibling}function td(e){switch(e.tag){case 26:Ta(e),e.flags&Sl&&e.memoizedState!==null&&q0(zt,e.memoizedState,e.memoizedProps);break;case 5:Ta(e);break;case 3:case 4:var t=zt;zt=Wi(e.stateNode.containerInfo),Ta(e),zt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Sl,Sl=16777216,Ta(e),Sl=t):Ta(e));break;default:Ta(e)}}function nd(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function xl(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var a=t[n];Ke=a,ld(a,e)}nd(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)ad(e),e=e.sibling}function ad(e){switch(e.tag){case 0:case 11:case 15:xl(e),e.flags&2048&&mn(9,e,e.return);break;case 3:xl(e);break;case 12:xl(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,qi(e)):xl(e);break;default:xl(e)}}function qi(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var a=t[n];Ke=a,ld(a,e)}nd(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:mn(8,t,t.return),qi(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,qi(t));break;default:qi(t)}e=e.sibling}}function ld(e,t){for(;Ke!==null;){var n=Ke;switch(n.tag){case 0:case 11:case 15:mn(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var a=n.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:al(n.memoizedState.cache)}if(a=n.child,a!==null)a.return=n,Ke=a;else e:for(n=e;Ke!==null;){a=Ke;var l=a.sibling,u=a.return;if($o(a),a===n){Ke=null;break e}if(l!==null){l.return=u,Ke=l;break e}Ke=u}}}var t0={getCacheForType:function(e){var t=et(Xe),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},n0=typeof WeakMap=="function"?WeakMap:Map,xe=0,Re=null,oe=null,me=0,Ee=0,vt=null,vn=!1,Aa=!1,uc=!1,tn=0,je=0,bn=0,Qn=0,sc=0,Mt=0,Na=0,El=null,ct=null,cc=!1,rc=0,Yi=1/0,Vi=null,Sn=null,Fe=0,xn=null,Ra=null,Ma=0,fc=0,oc=null,id=null,Ol=0,dc=null;function bt(){if((xe&2)!==0&&me!==0)return me&-me;if(_.T!==null){var e=ma;return e!==0?e:bc()}return br()}function ud(){Mt===0&&(Mt=(me&536870912)===0||be?mr():536870912);var e=Rt.current;return e!==null&&(e.flags|=32),Mt}function St(e,t,n){(e===Re&&(Ee===2||Ee===9)||e.cancelPendingCommit!==null)&&(_a(e,0),En(e,me,Mt,!1)),Va(e,n),((xe&2)===0||e!==Re)&&(e===Re&&((xe&2)===0&&(Qn|=n),je===4&&En(e,me,Mt,!1)),qt(e))}function sd(e,t,n){if((xe&6)!==0)throw Error(s(327));var a=!n&&(t&124)===0&&(t&e.expiredLanes)===0||Ya(e,t),l=a?i0(e,t):mc(e,t,!0),u=a;do{if(l===0){Aa&&!a&&En(e,t,0,!1);break}else{if(n=e.current.alternate,u&&!a0(n)){l=mc(e,t,!1),u=!1;continue}if(l===2){if(u=t,e.errorRecoveryDisabledLanes&u)var d=0;else d=e.pendingLanes&-536870913,d=d!==0?d:d&536870912?536870912:0;if(d!==0){t=d;e:{var g=e;l=El;var b=g.current.memoizedState.isDehydrated;if(b&&(_a(g,d).flags|=256),d=mc(g,d,!1),d!==2){if(uc&&!b){g.errorRecoveryDisabledLanes|=u,Qn|=u,l=4;break e}u=ct,ct=l,u!==null&&(ct===null?ct=u:ct.push.apply(ct,u))}l=d}if(u=!1,l!==2)continue}}if(l===1){_a(e,0),En(e,t,0,!0);break}e:{switch(a=e,u=l,u){case 0:case 1:throw Error(s(345));case 4:if((t&4194048)!==t)break;case 6:En(a,t,Mt,!vn);break e;case 2:ct=null;break;case 3:case 5:break;default:throw Error(s(329))}if((t&62914560)===t&&(l=rc+300-jt(),10<l)){if(En(a,t,Mt,!vn),Pl(a,0,!0)!==0)break e;a.timeoutHandle=Ld(cd.bind(null,a,n,ct,Vi,cc,t,Mt,Qn,Na,vn,u,2,-0,0),l);break e}cd(a,n,ct,Vi,cc,t,Mt,Qn,Na,vn,u,0,-0,0)}}break}while(!0);qt(e)}function cd(e,t,n,a,l,u,d,g,b,A,U,L,N,R){if(e.timeoutHandle=-1,L=t.subtreeFlags,(L&8192||(L&16785408)===16785408)&&(Dl={stylesheets:null,count:0,unsuspend:B0},td(t),L=Y0(),L!==null)){e.cancelPendingCommit=L(md.bind(null,e,t,u,n,a,l,d,g,b,U,1,N,R)),En(e,u,d,!A);return}md(e,t,u,n,a,l,d,g,b)}function a0(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var a=0;a<n.length;a++){var l=n[a],u=l.getSnapshot;l=l.value;try{if(!gt(u(),l))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function En(e,t,n,a){t&=~sc,t&=~Qn,e.suspendedLanes|=t,e.pingedLanes&=~t,a&&(e.warmLanes|=t),a=e.expirationTimes;for(var l=t;0<l;){var u=31-ht(l),d=1<<u;a[u]=-1,l&=~d}n!==0&&yr(e,n,t)}function Gi(){return(xe&6)===0?(Tl(0),!1):!0}function hc(){if(oe!==null){if(Ee===0)var e=oe.return;else e=oe,Kt=qn=null,Ds(e),xa=null,gl=0,e=oe;for(;e!==null;)Vo(e.alternate,e),e=e.return;oe=null}}function _a(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,x0(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),hc(),Re=e,oe=n=kt(e.current,null),me=t,Ee=0,vt=null,vn=!1,Aa=Ya(e,t),uc=!1,Na=Mt=sc=Qn=bn=je=0,ct=El=null,cc=!1,(t&8)!==0&&(t|=t&32);var a=e.entangledLanes;if(a!==0)for(e=e.entanglements,a&=t;0<a;){var l=31-ht(a),u=1<<l;t|=e[l],a&=~u}return tn=t,fi(),n}function rd(e,t){ce=null,_.H=_i,t===il||t===bi?(t=Af(),Ee=3):t===Ef?(t=Af(),Ee=4):Ee=t===Ro?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,vt=t,oe===null&&(je=1,ji(e,Ot(t,e.current)))}function fd(){var e=_.H;return _.H=_i,e===null?_i:e}function od(){var e=_.A;return _.A=t0,e}function gc(){je=4,vn||(me&4194048)!==me&&Rt.current!==null||(Aa=!0),(bn&134217727)===0&&(Qn&134217727)===0||Re===null||En(Re,me,Mt,!1)}function mc(e,t,n){var a=xe;xe|=2;var l=fd(),u=od();(Re!==e||me!==t)&&(Vi=null,_a(e,t)),t=!1;var d=je;e:do try{if(Ee!==0&&oe!==null){var g=oe,b=vt;switch(Ee){case 8:hc(),d=6;break e;case 3:case 2:case 9:case 6:Rt.current===null&&(t=!0);var A=Ee;if(Ee=0,vt=null,Da(e,g,b,A),n&&Aa){d=0;break e}break;default:A=Ee,Ee=0,vt=null,Da(e,g,b,A)}}l0(),d=je;break}catch(U){rd(e,U)}while(!0);return t&&e.shellSuspendCounter++,Kt=qn=null,xe=a,_.H=l,_.A=u,oe===null&&(Re=null,me=0,fi()),d}function l0(){for(;oe!==null;)dd(oe)}function i0(e,t){var n=xe;xe|=2;var a=fd(),l=od();Re!==e||me!==t?(Vi=null,Yi=jt()+500,_a(e,t)):Aa=Ya(e,t);e:do try{if(Ee!==0&&oe!==null){t=oe;var u=vt;t:switch(Ee){case 1:Ee=0,vt=null,Da(e,t,u,1);break;case 2:case 9:if(Of(u)){Ee=0,vt=null,hd(t);break}t=function(){Ee!==2&&Ee!==9||Re!==e||(Ee=7),qt(e)},u.then(t,t);break e;case 3:Ee=7;break e;case 4:Ee=5;break e;case 7:Of(u)?(Ee=0,vt=null,hd(t)):(Ee=0,vt=null,Da(e,t,u,7));break;case 5:var d=null;switch(oe.tag){case 26:d=oe.memoizedState;case 5:case 27:var g=oe;if(!d||Jd(d)){Ee=0,vt=null;var b=g.sibling;if(b!==null)oe=b;else{var A=g.return;A!==null?(oe=A,Xi(A)):oe=null}break t}}Ee=0,vt=null,Da(e,t,u,5);break;case 6:Ee=0,vt=null,Da(e,t,u,6);break;case 8:hc(),je=6;break e;default:throw Error(s(462))}}u0();break}catch(U){rd(e,U)}while(!0);return Kt=qn=null,_.H=a,_.A=l,xe=n,oe!==null?0:(Re=null,me=0,fi(),je)}function u0(){for(;oe!==null&&!Mg();)dd(oe)}function dd(e){var t=qo(e.alternate,e,tn);e.memoizedProps=e.pendingProps,t===null?Xi(e):oe=t}function hd(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Uo(n,t,t.pendingProps,t.type,void 0,me);break;case 11:t=Uo(n,t,t.pendingProps,t.type.render,t.ref,me);break;case 5:Ds(t);default:Vo(n,t),t=oe=hf(t,tn),t=qo(n,t,tn)}e.memoizedProps=e.pendingProps,t===null?Xi(e):oe=t}function Da(e,t,n,a){Kt=qn=null,Ds(t),xa=null,gl=0;var l=t.return;try{if($m(e,l,t,n,me)){je=1,ji(e,Ot(n,e.current)),oe=null;return}}catch(u){if(l!==null)throw oe=l,u;je=1,ji(e,Ot(n,e.current)),oe=null;return}t.flags&32768?(be||a===1?e=!0:Aa||(me&536870912)!==0?e=!1:(vn=e=!0,(a===2||a===9||a===3||a===6)&&(a=Rt.current,a!==null&&a.tag===13&&(a.flags|=16384))),gd(t,e)):Xi(t)}function Xi(e){var t=e;do{if((t.flags&32768)!==0){gd(t,vn);return}e=t.return;var n=Wm(t.alternate,t,tn);if(n!==null){oe=n;return}if(t=t.sibling,t!==null){oe=t;return}oe=t=e}while(t!==null);je===0&&(je=5)}function gd(e,t){do{var n=Pm(e.alternate,e);if(n!==null){n.flags&=32767,oe=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){oe=e;return}oe=e=n}while(e!==null);je=6,oe=null}function md(e,t,n,a,l,u,d,g,b){e.cancelPendingCommit=null;do ki();while(Fe!==0);if((xe&6)!==0)throw Error(s(327));if(t!==null){if(t===e.current)throw Error(s(177));if(u=t.lanes|t.childLanes,u|=is,Bg(e,n,u,d,g,b),e===Re&&(oe=Re=null,me=0),Ra=t,xn=e,Ma=n,fc=u,oc=l,id=a,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,f0($l,function(){return Sd(),null})):(e.callbackNode=null,e.callbackPriority=0),a=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||a){a=_.T,_.T=null,l=Y.p,Y.p=2,d=xe,xe|=4;try{Im(e,t,n)}finally{xe=d,Y.p=l,_.T=a}}Fe=1,pd(),yd(),vd()}}function pd(){if(Fe===1){Fe=0;var e=xn,t=Ra,n=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||n){n=_.T,_.T=null;var a=Y.p;Y.p=2;var l=xe;xe|=4;try{Po(t,e);var u=Rc,d=nf(e.containerInfo),g=u.focusedElem,b=u.selectionRange;if(d!==g&&g&&g.ownerDocument&&tf(g.ownerDocument.documentElement,g)){if(b!==null&&es(g)){var A=b.start,U=b.end;if(U===void 0&&(U=A),"selectionStart"in g)g.selectionStart=A,g.selectionEnd=Math.min(U,g.value.length);else{var L=g.ownerDocument||document,N=L&&L.defaultView||window;if(N.getSelection){var R=N.getSelection(),ee=g.textContent.length,W=Math.min(b.start,ee),Ae=b.end===void 0?W:Math.min(b.end,ee);!R.extend&&W>Ae&&(d=Ae,Ae=W,W=d);var O=ef(g,W),x=ef(g,Ae);if(O&&x&&(R.rangeCount!==1||R.anchorNode!==O.node||R.anchorOffset!==O.offset||R.focusNode!==x.node||R.focusOffset!==x.offset)){var T=L.createRange();T.setStart(O.node,O.offset),R.removeAllRanges(),W>Ae?(R.addRange(T),R.extend(x.node,x.offset)):(T.setEnd(x.node,x.offset),R.addRange(T))}}}}for(L=[],R=g;R=R.parentNode;)R.nodeType===1&&L.push({element:R,left:R.scrollLeft,top:R.scrollTop});for(typeof g.focus=="function"&&g.focus(),g=0;g<L.length;g++){var j=L[g];j.element.scrollLeft=j.left,j.element.scrollTop=j.top}}nu=!!Nc,Rc=Nc=null}finally{xe=l,Y.p=a,_.T=n}}e.current=t,Fe=2}}function yd(){if(Fe===2){Fe=0;var e=xn,t=Ra,n=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||n){n=_.T,_.T=null;var a=Y.p;Y.p=2;var l=xe;xe|=4;try{Jo(e,t.alternate,t)}finally{xe=l,Y.p=a,_.T=n}}Fe=3}}function vd(){if(Fe===4||Fe===3){Fe=0,_g();var e=xn,t=Ra,n=Ma,a=id;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Fe=5:(Fe=0,Ra=xn=null,bd(e,e.pendingLanes));var l=e.pendingLanes;if(l===0&&(Sn=null),Uu(n),t=t.stateNode,dt&&typeof dt.onCommitFiberRoot=="function")try{dt.onCommitFiberRoot(qa,t,void 0,(t.current.flags&128)===128)}catch{}if(a!==null){t=_.T,l=Y.p,Y.p=2,_.T=null;try{for(var u=e.onRecoverableError,d=0;d<a.length;d++){var g=a[d];u(g.value,{componentStack:g.stack})}}finally{_.T=t,Y.p=l}}(Ma&3)!==0&&ki(),qt(e),l=e.pendingLanes,(n&4194090)!==0&&(l&42)!==0?e===dc?Ol++:(Ol=0,dc=e):Ol=0,Tl(0)}}function bd(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,al(t)))}function ki(e){return pd(),yd(),vd(),Sd()}function Sd(){if(Fe!==5)return!1;var e=xn,t=fc;fc=0;var n=Uu(Ma),a=_.T,l=Y.p;try{Y.p=32>n?32:n,_.T=null,n=oc,oc=null;var u=xn,d=Ma;if(Fe=0,Ra=xn=null,Ma=0,(xe&6)!==0)throw Error(s(331));var g=xe;if(xe|=4,ad(u.current),ed(u,u.current,d,n),xe=g,Tl(0,!1),dt&&typeof dt.onPostCommitFiberRoot=="function")try{dt.onPostCommitFiberRoot(qa,u)}catch{}return!0}finally{Y.p=l,_.T=a,bd(e,t)}}function xd(e,t,n){t=Ot(n,t),t=ks(e.stateNode,t,2),e=on(e,t,2),e!==null&&(Va(e,2),qt(e))}function Ne(e,t,n){if(e.tag===3)xd(e,e,n);else for(;t!==null;){if(t.tag===3){xd(t,e,n);break}else if(t.tag===1){var a=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(Sn===null||!Sn.has(a))){e=Ot(n,e),n=Ao(2),a=on(t,n,2),a!==null&&(No(n,a,t,e),Va(a,2),qt(a));break}}t=t.return}}function pc(e,t,n){var a=e.pingCache;if(a===null){a=e.pingCache=new n0;var l=new Set;a.set(t,l)}else l=a.get(t),l===void 0&&(l=new Set,a.set(t,l));l.has(n)||(uc=!0,l.add(n),e=s0.bind(null,e,t,n),t.then(e,e))}function s0(e,t,n){var a=e.pingCache;a!==null&&a.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,Re===e&&(me&n)===n&&(je===4||je===3&&(me&62914560)===me&&300>jt()-rc?(xe&2)===0&&_a(e,0):sc|=n,Na===me&&(Na=0)),qt(e)}function Ed(e,t){t===0&&(t=pr()),e=oa(e,t),e!==null&&(Va(e,t),qt(e))}function c0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Ed(e,n)}function r0(e,t){var n=0;switch(e.tag){case 13:var a=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error(s(314))}a!==null&&a.delete(t),Ed(e,n)}function f0(e,t){return _u(e,t)}var Qi=null,za=null,yc=!1,Zi=!1,vc=!1,Zn=0;function qt(e){e!==za&&e.next===null&&(za===null?Qi=za=e:za=za.next=e),Zi=!0,yc||(yc=!0,d0())}function Tl(e,t){if(!vc&&Zi){vc=!0;do for(var n=!1,a=Qi;a!==null;){if(e!==0){var l=a.pendingLanes;if(l===0)var u=0;else{var d=a.suspendedLanes,g=a.pingedLanes;u=(1<<31-ht(42|e)+1)-1,u&=l&~(d&~g),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(n=!0,Nd(a,u))}else u=me,u=Pl(a,a===Re?u:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(u&3)===0||Ya(a,u)||(n=!0,Nd(a,u));a=a.next}while(n);vc=!1}}function o0(){Od()}function Od(){Zi=yc=!1;var e=0;Zn!==0&&(S0()&&(e=Zn),Zn=0);for(var t=jt(),n=null,a=Qi;a!==null;){var l=a.next,u=Td(a,t);u===0?(a.next=null,n===null?Qi=l:n.next=l,l===null&&(za=n)):(n=a,(e!==0||(u&3)!==0)&&(Zi=!0)),a=l}Tl(e)}function Td(e,t){for(var n=e.suspendedLanes,a=e.pingedLanes,l=e.expirationTimes,u=e.pendingLanes&-62914561;0<u;){var d=31-ht(u),g=1<<d,b=l[d];b===-1?((g&n)===0||(g&a)!==0)&&(l[d]=Hg(g,t)):b<=t&&(e.expiredLanes|=g),u&=~g}if(t=Re,n=me,n=Pl(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a=e.callbackNode,n===0||e===t&&(Ee===2||Ee===9)||e.cancelPendingCommit!==null)return a!==null&&a!==null&&Du(a),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||Ya(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(a!==null&&Du(a),Uu(n)){case 2:case 8:n=hr;break;case 32:n=$l;break;case 268435456:n=gr;break;default:n=$l}return a=Ad.bind(null,e),n=_u(n,a),e.callbackPriority=t,e.callbackNode=n,t}return a!==null&&a!==null&&Du(a),e.callbackPriority=2,e.callbackNode=null,2}function Ad(e,t){if(Fe!==0&&Fe!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(ki()&&e.callbackNode!==n)return null;var a=me;return a=Pl(e,e===Re?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a===0?null:(sd(e,a,t),Td(e,jt()),e.callbackNode!=null&&e.callbackNode===n?Ad.bind(null,e):null)}function Nd(e,t){if(ki())return null;sd(e,t,!0)}function d0(){E0(function(){(xe&6)!==0?_u(dr,o0):Od()})}function bc(){return Zn===0&&(Zn=mr()),Zn}function Rd(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:ai(""+e)}function Md(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function h0(e,t,n,a,l){if(t==="submit"&&n&&n.stateNode===l){var u=Rd((l[lt]||null).action),d=a.submitter;d&&(t=(t=d[lt]||null)?Rd(t.formAction):d.getAttribute("formAction"),t!==null&&(u=t,d=null));var g=new si("action","action",null,a,l);e.push({event:g,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(Zn!==0){var b=d?Md(l,d):new FormData(l);qs(n,{pending:!0,data:b,method:l.method,action:u},null,b)}}else typeof u=="function"&&(g.preventDefault(),b=d?Md(l,d):new FormData(l),qs(n,{pending:!0,data:b,method:l.method,action:u},u,b))},currentTarget:l}]})}}for(var Sc=0;Sc<ls.length;Sc++){var xc=ls[Sc],g0=xc.toLowerCase(),m0=xc[0].toUpperCase()+xc.slice(1);Dt(g0,"on"+m0)}Dt(uf,"onAnimationEnd"),Dt(sf,"onAnimationIteration"),Dt(cf,"onAnimationStart"),Dt("dblclick","onDoubleClick"),Dt("focusin","onFocus"),Dt("focusout","onBlur"),Dt(wm,"onTransitionRun"),Dt(Um,"onTransitionStart"),Dt(jm,"onTransitionCancel"),Dt(rf,"onTransitionEnd"),ta("onMouseEnter",["mouseout","mouseover"]),ta("onMouseLeave",["mouseout","mouseover"]),ta("onPointerEnter",["pointerout","pointerover"]),ta("onPointerLeave",["pointerout","pointerover"]),Dn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Dn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Dn("onBeforeInput",["compositionend","keypress","textInput","paste"]),Dn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Dn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Dn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Al="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),p0=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Al));function _d(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var a=e[n],l=a.event;a=a.listeners;e:{var u=void 0;if(t)for(var d=a.length-1;0<=d;d--){var g=a[d],b=g.instance,A=g.currentTarget;if(g=g.listener,b!==u&&l.isPropagationStopped())break e;u=g,l.currentTarget=A;try{u(l)}catch(U){Ui(U)}l.currentTarget=null,u=b}else for(d=0;d<a.length;d++){if(g=a[d],b=g.instance,A=g.currentTarget,g=g.listener,b!==u&&l.isPropagationStopped())break e;u=g,l.currentTarget=A;try{u(l)}catch(U){Ui(U)}l.currentTarget=null,u=b}}}}function de(e,t){var n=t[ju];n===void 0&&(n=t[ju]=new Set);var a=e+"__bubble";n.has(a)||(Dd(t,e,2,!1),n.add(a))}function Ec(e,t,n){var a=0;t&&(a|=4),Dd(n,e,a,t)}var Ki="_reactListening"+Math.random().toString(36).slice(2);function Oc(e){if(!e[Ki]){e[Ki]=!0,xr.forEach(function(n){n!=="selectionchange"&&(p0.has(n)||Ec(n,!1,e),Ec(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ki]||(t[Ki]=!0,Ec("selectionchange",!1,t))}}function Dd(e,t,n,a){switch(eh(t)){case 2:var l=X0;break;case 8:l=k0;break;default:l=Hc}n=l.bind(null,t,n,e),l=void 0,!Qu||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),a?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function Tc(e,t,n,a,l){var u=a;if((t&1)===0&&(t&2)===0&&a!==null)e:for(;;){if(a===null)return;var d=a.tag;if(d===3||d===4){var g=a.stateNode.containerInfo;if(g===l)break;if(d===4)for(d=a.return;d!==null;){var b=d.tag;if((b===3||b===4)&&d.stateNode.containerInfo===l)return;d=d.return}for(;g!==null;){if(d=Pn(g),d===null)return;if(b=d.tag,b===5||b===6||b===26||b===27){a=u=d;continue e}g=g.parentNode}}a=a.return}Cr(function(){var A=u,U=Xu(n),L=[];e:{var N=ff.get(e);if(N!==void 0){var R=si,ee=e;switch(e){case"keypress":if(ii(n)===0)break e;case"keydown":case"keyup":R=fm;break;case"focusin":ee="focus",R=$u;break;case"focusout":ee="blur",R=$u;break;case"beforeblur":case"afterblur":R=$u;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":R=Br;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":R=Pg;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":R=hm;break;case uf:case sf:case cf:R=tm;break;case rf:R=mm;break;case"scroll":case"scrollend":R=Fg;break;case"wheel":R=ym;break;case"copy":case"cut":case"paste":R=am;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":R=Yr;break;case"toggle":case"beforetoggle":R=bm}var W=(t&4)!==0,Ae=!W&&(e==="scroll"||e==="scrollend"),O=W?N!==null?N+"Capture":null:N;W=[];for(var x=A,T;x!==null;){var j=x;if(T=j.stateNode,j=j.tag,j!==5&&j!==26&&j!==27||T===null||O===null||(j=ka(x,O),j!=null&&W.push(Nl(x,j,T))),Ae)break;x=x.return}0<W.length&&(N=new R(N,ee,null,n,U),L.push({event:N,listeners:W}))}}if((t&7)===0){e:{if(N=e==="mouseover"||e==="pointerover",R=e==="mouseout"||e==="pointerout",N&&n!==Gu&&(ee=n.relatedTarget||n.fromElement)&&(Pn(ee)||ee[Wn]))break e;if((R||N)&&(N=U.window===U?U:(N=U.ownerDocument)?N.defaultView||N.parentWindow:window,R?(ee=n.relatedTarget||n.toElement,R=A,ee=ee?Pn(ee):null,ee!==null&&(Ae=o(ee),W=ee.tag,ee!==Ae||W!==5&&W!==27&&W!==6)&&(ee=null)):(R=null,ee=A),R!==ee)){if(W=Br,j="onMouseLeave",O="onMouseEnter",x="mouse",(e==="pointerout"||e==="pointerover")&&(W=Yr,j="onPointerLeave",O="onPointerEnter",x="pointer"),Ae=R==null?N:Xa(R),T=ee==null?N:Xa(ee),N=new W(j,x+"leave",R,n,U),N.target=Ae,N.relatedTarget=T,j=null,Pn(U)===A&&(W=new W(O,x+"enter",ee,n,U),W.target=T,W.relatedTarget=Ae,j=W),Ae=j,R&&ee)t:{for(W=R,O=ee,x=0,T=W;T;T=wa(T))x++;for(T=0,j=O;j;j=wa(j))T++;for(;0<x-T;)W=wa(W),x--;for(;0<T-x;)O=wa(O),T--;for(;x--;){if(W===O||O!==null&&W===O.alternate)break t;W=wa(W),O=wa(O)}W=null}else W=null;R!==null&&zd(L,N,R,W,!1),ee!==null&&Ae!==null&&zd(L,Ae,ee,W,!0)}}e:{if(N=A?Xa(A):window,R=N.nodeName&&N.nodeName.toLowerCase(),R==="select"||R==="input"&&N.type==="file")var X=Jr;else if(Zr(N))if($r)X=_m;else{X=Rm;var re=Nm}else R=N.nodeName,!R||R.toLowerCase()!=="input"||N.type!=="checkbox"&&N.type!=="radio"?A&&Vu(A.elementType)&&(X=Jr):X=Mm;if(X&&(X=X(e,A))){Kr(L,X,n,U);break e}re&&re(e,N,A),e==="focusout"&&A&&N.type==="number"&&A.memoizedProps.value!=null&&Yu(N,"number",N.value)}switch(re=A?Xa(A):window,e){case"focusin":(Zr(re)||re.contentEditable==="true")&&(ca=re,ts=A,Pa=null);break;case"focusout":Pa=ts=ca=null;break;case"mousedown":ns=!0;break;case"contextmenu":case"mouseup":case"dragend":ns=!1,af(L,n,U);break;case"selectionchange":if(zm)break;case"keydown":case"keyup":af(L,n,U)}var Z;if(Wu)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else sa?kr(e,n)&&(P="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(P="onCompositionStart");P&&(Vr&&n.locale!=="ko"&&(sa||P!=="onCompositionStart"?P==="onCompositionEnd"&&sa&&(Z=Lr()):(sn=U,Zu="value"in sn?sn.value:sn.textContent,sa=!0)),re=Ji(A,P),0<re.length&&(P=new qr(P,e,null,n,U),L.push({event:P,listeners:re}),Z?P.data=Z:(Z=Qr(n),Z!==null&&(P.data=Z)))),(Z=xm?Em(e,n):Om(e,n))&&(P=Ji(A,"onBeforeInput"),0<P.length&&(re=new qr("onBeforeInput","beforeinput",null,n,U),L.push({event:re,listeners:P}),re.data=Z)),h0(L,e,A,n,U)}_d(L,t)})}function Nl(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ji(e,t){for(var n=t+"Capture",a=[];e!==null;){var l=e,u=l.stateNode;if(l=l.tag,l!==5&&l!==26&&l!==27||u===null||(l=ka(e,n),l!=null&&a.unshift(Nl(e,l,u)),l=ka(e,t),l!=null&&a.push(Nl(e,l,u))),e.tag===3)return a;e=e.return}return[]}function wa(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function zd(e,t,n,a,l){for(var u=t._reactName,d=[];n!==null&&n!==a;){var g=n,b=g.alternate,A=g.stateNode;if(g=g.tag,b!==null&&b===a)break;g!==5&&g!==26&&g!==27||A===null||(b=A,l?(A=ka(n,u),A!=null&&d.unshift(Nl(n,A,b))):l||(A=ka(n,u),A!=null&&d.push(Nl(n,A,b)))),n=n.return}d.length!==0&&e.push({event:t,listeners:d})}var y0=/\r\n?/g,v0=/\u0000|\uFFFD/g;function wd(e){return(typeof e=="string"?e:""+e).replace(y0,`
`).replace(v0,"")}function Ud(e,t){return t=wd(t),wd(e)===t}function $i(){}function Te(e,t,n,a,l,u){switch(n){case"children":typeof a=="string"?t==="body"||t==="textarea"&&a===""||la(e,a):(typeof a=="number"||typeof a=="bigint")&&t!=="body"&&la(e,""+a);break;case"className":ei(e,"class",a);break;case"tabIndex":ei(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":ei(e,n,a);break;case"style":Ur(e,a,u);break;case"data":if(t!=="object"){ei(e,"data",a);break}case"src":case"href":if(a===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(n);break}a=ai(""+a),e.setAttribute(n,a);break;case"action":case"formAction":if(typeof a=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(n==="formAction"?(t!=="input"&&Te(e,t,"name",l.name,l,null),Te(e,t,"formEncType",l.formEncType,l,null),Te(e,t,"formMethod",l.formMethod,l,null),Te(e,t,"formTarget",l.formTarget,l,null)):(Te(e,t,"encType",l.encType,l,null),Te(e,t,"method",l.method,l,null),Te(e,t,"target",l.target,l,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(n);break}a=ai(""+a),e.setAttribute(n,a);break;case"onClick":a!=null&&(e.onclick=$i);break;case"onScroll":a!=null&&de("scroll",e);break;case"onScrollEnd":a!=null&&de("scrollend",e);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(s(61));if(n=a.__html,n!=null){if(l.children!=null)throw Error(s(60));e.innerHTML=n}}break;case"multiple":e.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":e.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){e.removeAttribute("xlink:href");break}n=ai(""+a),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(n,""+a):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":a===!0?e.setAttribute(n,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(n,a):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?e.setAttribute(n,a):e.removeAttribute(n);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?e.removeAttribute(n):e.setAttribute(n,a);break;case"popover":de("beforetoggle",e),de("toggle",e),Il(e,"popover",a);break;case"xlinkActuate":Gt(e,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":Gt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":Gt(e,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":Gt(e,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":Gt(e,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":Gt(e,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":Gt(e,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":Gt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":Gt(e,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Il(e,"is",a);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Jg.get(n)||n,Il(e,n,a))}}function Ac(e,t,n,a,l,u){switch(n){case"style":Ur(e,a,u);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(s(61));if(n=a.__html,n!=null){if(l.children!=null)throw Error(s(60));e.innerHTML=n}}break;case"children":typeof a=="string"?la(e,a):(typeof a=="number"||typeof a=="bigint")&&la(e,""+a);break;case"onScroll":a!=null&&de("scroll",e);break;case"onScrollEnd":a!=null&&de("scrollend",e);break;case"onClick":a!=null&&(e.onclick=$i);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Er.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(l=n.endsWith("Capture"),t=n.slice(2,l?n.length-7:void 0),u=e[lt]||null,u=u!=null?u[n]:null,typeof u=="function"&&e.removeEventListener(t,u,l),typeof a=="function")){typeof u!="function"&&u!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,a,l);break e}n in e?e[n]=a:a===!0?e.setAttribute(n,""):Il(e,n,a)}}}function We(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":de("error",e),de("load",e);var a=!1,l=!1,u;for(u in n)if(n.hasOwnProperty(u)){var d=n[u];if(d!=null)switch(u){case"src":a=!0;break;case"srcSet":l=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:Te(e,t,u,d,n,null)}}l&&Te(e,t,"srcSet",n.srcSet,n,null),a&&Te(e,t,"src",n.src,n,null);return;case"input":de("invalid",e);var g=u=d=l=null,b=null,A=null;for(a in n)if(n.hasOwnProperty(a)){var U=n[a];if(U!=null)switch(a){case"name":l=U;break;case"type":d=U;break;case"checked":b=U;break;case"defaultChecked":A=U;break;case"value":u=U;break;case"defaultValue":g=U;break;case"children":case"dangerouslySetInnerHTML":if(U!=null)throw Error(s(137,t));break;default:Te(e,t,a,U,n,null)}}_r(e,u,g,b,A,d,l,!1),ti(e);return;case"select":de("invalid",e),a=d=u=null;for(l in n)if(n.hasOwnProperty(l)&&(g=n[l],g!=null))switch(l){case"value":u=g;break;case"defaultValue":d=g;break;case"multiple":a=g;default:Te(e,t,l,g,n,null)}t=u,n=d,e.multiple=!!a,t!=null?aa(e,!!a,t,!1):n!=null&&aa(e,!!a,n,!0);return;case"textarea":de("invalid",e),u=l=a=null;for(d in n)if(n.hasOwnProperty(d)&&(g=n[d],g!=null))switch(d){case"value":a=g;break;case"defaultValue":l=g;break;case"children":u=g;break;case"dangerouslySetInnerHTML":if(g!=null)throw Error(s(91));break;default:Te(e,t,d,g,n,null)}zr(e,a,l,u),ti(e);return;case"option":for(b in n)if(n.hasOwnProperty(b)&&(a=n[b],a!=null))switch(b){case"selected":e.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:Te(e,t,b,a,n,null)}return;case"dialog":de("beforetoggle",e),de("toggle",e),de("cancel",e),de("close",e);break;case"iframe":case"object":de("load",e);break;case"video":case"audio":for(a=0;a<Al.length;a++)de(Al[a],e);break;case"image":de("error",e),de("load",e);break;case"details":de("toggle",e);break;case"embed":case"source":case"link":de("error",e),de("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(A in n)if(n.hasOwnProperty(A)&&(a=n[A],a!=null))switch(A){case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:Te(e,t,A,a,n,null)}return;default:if(Vu(t)){for(U in n)n.hasOwnProperty(U)&&(a=n[U],a!==void 0&&Ac(e,t,U,a,n,void 0));return}}for(g in n)n.hasOwnProperty(g)&&(a=n[g],a!=null&&Te(e,t,g,a,n,null))}function b0(e,t,n,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var l=null,u=null,d=null,g=null,b=null,A=null,U=null;for(R in n){var L=n[R];if(n.hasOwnProperty(R)&&L!=null)switch(R){case"checked":break;case"value":break;case"defaultValue":b=L;default:a.hasOwnProperty(R)||Te(e,t,R,null,a,L)}}for(var N in a){var R=a[N];if(L=n[N],a.hasOwnProperty(N)&&(R!=null||L!=null))switch(N){case"type":u=R;break;case"name":l=R;break;case"checked":A=R;break;case"defaultChecked":U=R;break;case"value":d=R;break;case"defaultValue":g=R;break;case"children":case"dangerouslySetInnerHTML":if(R!=null)throw Error(s(137,t));break;default:R!==L&&Te(e,t,N,R,a,L)}}qu(e,d,g,b,A,U,u,l);return;case"select":R=d=g=N=null;for(u in n)if(b=n[u],n.hasOwnProperty(u)&&b!=null)switch(u){case"value":break;case"multiple":R=b;default:a.hasOwnProperty(u)||Te(e,t,u,null,a,b)}for(l in a)if(u=a[l],b=n[l],a.hasOwnProperty(l)&&(u!=null||b!=null))switch(l){case"value":N=u;break;case"defaultValue":g=u;break;case"multiple":d=u;default:u!==b&&Te(e,t,l,u,a,b)}t=g,n=d,a=R,N!=null?aa(e,!!n,N,!1):!!a!=!!n&&(t!=null?aa(e,!!n,t,!0):aa(e,!!n,n?[]:"",!1));return;case"textarea":R=N=null;for(g in n)if(l=n[g],n.hasOwnProperty(g)&&l!=null&&!a.hasOwnProperty(g))switch(g){case"value":break;case"children":break;default:Te(e,t,g,null,a,l)}for(d in a)if(l=a[d],u=n[d],a.hasOwnProperty(d)&&(l!=null||u!=null))switch(d){case"value":N=l;break;case"defaultValue":R=l;break;case"children":break;case"dangerouslySetInnerHTML":if(l!=null)throw Error(s(91));break;default:l!==u&&Te(e,t,d,l,a,u)}Dr(e,N,R);return;case"option":for(var ee in n)if(N=n[ee],n.hasOwnProperty(ee)&&N!=null&&!a.hasOwnProperty(ee))switch(ee){case"selected":e.selected=!1;break;default:Te(e,t,ee,null,a,N)}for(b in a)if(N=a[b],R=n[b],a.hasOwnProperty(b)&&N!==R&&(N!=null||R!=null))switch(b){case"selected":e.selected=N&&typeof N!="function"&&typeof N!="symbol";break;default:Te(e,t,b,N,a,R)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var W in n)N=n[W],n.hasOwnProperty(W)&&N!=null&&!a.hasOwnProperty(W)&&Te(e,t,W,null,a,N);for(A in a)if(N=a[A],R=n[A],a.hasOwnProperty(A)&&N!==R&&(N!=null||R!=null))switch(A){case"children":case"dangerouslySetInnerHTML":if(N!=null)throw Error(s(137,t));break;default:Te(e,t,A,N,a,R)}return;default:if(Vu(t)){for(var Ae in n)N=n[Ae],n.hasOwnProperty(Ae)&&N!==void 0&&!a.hasOwnProperty(Ae)&&Ac(e,t,Ae,void 0,a,N);for(U in a)N=a[U],R=n[U],!a.hasOwnProperty(U)||N===R||N===void 0&&R===void 0||Ac(e,t,U,N,a,R);return}}for(var O in n)N=n[O],n.hasOwnProperty(O)&&N!=null&&!a.hasOwnProperty(O)&&Te(e,t,O,null,a,N);for(L in a)N=a[L],R=n[L],!a.hasOwnProperty(L)||N===R||N==null&&R==null||Te(e,t,L,N,a,R)}var Nc=null,Rc=null;function Fi(e){return e.nodeType===9?e:e.ownerDocument}function jd(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Cd(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Mc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var _c=null;function S0(){var e=window.event;return e&&e.type==="popstate"?e===_c?!1:(_c=e,!0):(_c=null,!1)}var Ld=typeof setTimeout=="function"?setTimeout:void 0,x0=typeof clearTimeout=="function"?clearTimeout:void 0,Hd=typeof Promise=="function"?Promise:void 0,E0=typeof queueMicrotask=="function"?queueMicrotask:typeof Hd<"u"?function(e){return Hd.resolve(null).then(e).catch(O0)}:Ld;function O0(e){setTimeout(function(){throw e})}function On(e){return e==="head"}function Bd(e,t){var n=t,a=0,l=0;do{var u=n.nextSibling;if(e.removeChild(n),u&&u.nodeType===8)if(n=u.data,n==="/$"){if(0<a&&8>a){n=a;var d=e.ownerDocument;if(n&1&&Rl(d.documentElement),n&2&&Rl(d.body),n&4)for(n=d.head,Rl(n),d=n.firstChild;d;){var g=d.nextSibling,b=d.nodeName;d[Ga]||b==="SCRIPT"||b==="STYLE"||b==="LINK"&&d.rel.toLowerCase()==="stylesheet"||n.removeChild(d),d=g}}if(l===0){e.removeChild(u),Cl(t);return}l--}else n==="$"||n==="$?"||n==="$!"?l++:a=n.charCodeAt(0)-48;else a=0;n=u}while(n);Cl(t)}function Dc(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Dc(n),Cu(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function T0(e,t,n,a){for(;e.nodeType===1;){var l=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!a&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(a){if(!e[Ga])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(u=e.getAttribute("rel"),u==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(u!==l.rel||e.getAttribute("href")!==(l.href==null||l.href===""?null:l.href)||e.getAttribute("crossorigin")!==(l.crossOrigin==null?null:l.crossOrigin)||e.getAttribute("title")!==(l.title==null?null:l.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(u=e.getAttribute("src"),(u!==(l.src==null?null:l.src)||e.getAttribute("type")!==(l.type==null?null:l.type)||e.getAttribute("crossorigin")!==(l.crossOrigin==null?null:l.crossOrigin))&&u&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var u=l.name==null?null:""+l.name;if(l.type==="hidden"&&e.getAttribute("name")===u)return e}else return e;if(e=wt(e.nextSibling),e===null)break}return null}function A0(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=wt(e.nextSibling),e===null))return null;return e}function zc(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function N0(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var a=function(){t(),n.removeEventListener("DOMContentLoaded",a)};n.addEventListener("DOMContentLoaded",a),e._reactRetry=a}}function wt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var wc=null;function qd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function Yd(e,t,n){switch(t=Fi(n),e){case"html":if(e=t.documentElement,!e)throw Error(s(452));return e;case"head":if(e=t.head,!e)throw Error(s(453));return e;case"body":if(e=t.body,!e)throw Error(s(454));return e;default:throw Error(s(451))}}function Rl(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Cu(e)}var _t=new Map,Vd=new Set;function Wi(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var nn=Y.d;Y.d={f:R0,r:M0,D:_0,C:D0,L:z0,m:w0,X:j0,S:U0,M:C0};function R0(){var e=nn.f(),t=Gi();return e||t}function M0(e){var t=In(e);t!==null&&t.tag===5&&t.type==="form"?uo(t):nn.r(e)}var Ua=typeof document>"u"?null:document;function Gd(e,t,n){var a=Ua;if(a&&typeof t=="string"&&t){var l=Et(t);l='link[rel="'+e+'"][href="'+l+'"]',typeof n=="string"&&(l+='[crossorigin="'+n+'"]'),Vd.has(l)||(Vd.add(l),e={rel:e,crossOrigin:n,href:t},a.querySelector(l)===null&&(t=a.createElement("link"),We(t,"link",e),Qe(t),a.head.appendChild(t)))}}function _0(e){nn.D(e),Gd("dns-prefetch",e,null)}function D0(e,t){nn.C(e,t),Gd("preconnect",e,t)}function z0(e,t,n){nn.L(e,t,n);var a=Ua;if(a&&e&&t){var l='link[rel="preload"][as="'+Et(t)+'"]';t==="image"&&n&&n.imageSrcSet?(l+='[imagesrcset="'+Et(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(l+='[imagesizes="'+Et(n.imageSizes)+'"]')):l+='[href="'+Et(e)+'"]';var u=l;switch(t){case"style":u=ja(e);break;case"script":u=Ca(e)}_t.has(u)||(e=v({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),_t.set(u,e),a.querySelector(l)!==null||t==="style"&&a.querySelector(Ml(u))||t==="script"&&a.querySelector(_l(u))||(t=a.createElement("link"),We(t,"link",e),Qe(t),a.head.appendChild(t)))}}function w0(e,t){nn.m(e,t);var n=Ua;if(n&&e){var a=t&&typeof t.as=="string"?t.as:"script",l='link[rel="modulepreload"][as="'+Et(a)+'"][href="'+Et(e)+'"]',u=l;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=Ca(e)}if(!_t.has(u)&&(e=v({rel:"modulepreload",href:e},t),_t.set(u,e),n.querySelector(l)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(_l(u)))return}a=n.createElement("link"),We(a,"link",e),Qe(a),n.head.appendChild(a)}}}function U0(e,t,n){nn.S(e,t,n);var a=Ua;if(a&&e){var l=ea(a).hoistableStyles,u=ja(e);t=t||"default";var d=l.get(u);if(!d){var g={loading:0,preload:null};if(d=a.querySelector(Ml(u)))g.loading=5;else{e=v({rel:"stylesheet",href:e,"data-precedence":t},n),(n=_t.get(u))&&Uc(e,n);var b=d=a.createElement("link");Qe(b),We(b,"link",e),b._p=new Promise(function(A,U){b.onload=A,b.onerror=U}),b.addEventListener("load",function(){g.loading|=1}),b.addEventListener("error",function(){g.loading|=2}),g.loading|=4,Pi(d,t,a)}d={type:"stylesheet",instance:d,count:1,state:g},l.set(u,d)}}}function j0(e,t){nn.X(e,t);var n=Ua;if(n&&e){var a=ea(n).hoistableScripts,l=Ca(e),u=a.get(l);u||(u=n.querySelector(_l(l)),u||(e=v({src:e,async:!0},t),(t=_t.get(l))&&jc(e,t),u=n.createElement("script"),Qe(u),We(u,"link",e),n.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(l,u))}}function C0(e,t){nn.M(e,t);var n=Ua;if(n&&e){var a=ea(n).hoistableScripts,l=Ca(e),u=a.get(l);u||(u=n.querySelector(_l(l)),u||(e=v({src:e,async:!0,type:"module"},t),(t=_t.get(l))&&jc(e,t),u=n.createElement("script"),Qe(u),We(u,"link",e),n.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(l,u))}}function Xd(e,t,n,a){var l=(l=ne.current)?Wi(l):null;if(!l)throw Error(s(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=ja(n.href),n=ea(l).hoistableStyles,a=n.get(t),a||(a={type:"style",instance:null,count:0,state:null},n.set(t,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=ja(n.href);var u=ea(l).hoistableStyles,d=u.get(e);if(d||(l=l.ownerDocument||l,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,d),(u=l.querySelector(Ml(e)))&&!u._p&&(d.instance=u,d.state.loading=5),_t.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},_t.set(e,n),u||L0(l,e,n,d.state))),t&&a===null)throw Error(s(528,""));return d}if(t&&a!==null)throw Error(s(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Ca(n),n=ea(l).hoistableScripts,a=n.get(t),a||(a={type:"script",instance:null,count:0,state:null},n.set(t,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(s(444,e))}}function ja(e){return'href="'+Et(e)+'"'}function Ml(e){return'link[rel="stylesheet"]['+e+"]"}function kd(e){return v({},e,{"data-precedence":e.precedence,precedence:null})}function L0(e,t,n,a){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?a.loading=1:(t=e.createElement("link"),a.preload=t,t.addEventListener("load",function(){return a.loading|=1}),t.addEventListener("error",function(){return a.loading|=2}),We(t,"link",n),Qe(t),e.head.appendChild(t))}function Ca(e){return'[src="'+Et(e)+'"]'}function _l(e){return"script[async]"+e}function Qd(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var a=e.querySelector('style[data-href~="'+Et(n.href)+'"]');if(a)return t.instance=a,Qe(a),a;var l=v({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return a=(e.ownerDocument||e).createElement("style"),Qe(a),We(a,"style",l),Pi(a,n.precedence,e),t.instance=a;case"stylesheet":l=ja(n.href);var u=e.querySelector(Ml(l));if(u)return t.state.loading|=4,t.instance=u,Qe(u),u;a=kd(n),(l=_t.get(l))&&Uc(a,l),u=(e.ownerDocument||e).createElement("link"),Qe(u);var d=u;return d._p=new Promise(function(g,b){d.onload=g,d.onerror=b}),We(u,"link",a),t.state.loading|=4,Pi(u,n.precedence,e),t.instance=u;case"script":return u=Ca(n.src),(l=e.querySelector(_l(u)))?(t.instance=l,Qe(l),l):(a=n,(l=_t.get(u))&&(a=v({},n),jc(a,l)),e=e.ownerDocument||e,l=e.createElement("script"),Qe(l),We(l,"link",a),e.head.appendChild(l),t.instance=l);case"void":return null;default:throw Error(s(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(a=t.instance,t.state.loading|=4,Pi(a,n.precedence,e));return t.instance}function Pi(e,t,n){for(var a=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),l=a.length?a[a.length-1]:null,u=l,d=0;d<a.length;d++){var g=a[d];if(g.dataset.precedence===t)u=g;else if(u!==l)break}u?u.parentNode.insertBefore(e,u.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function Uc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function jc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Ii=null;function Zd(e,t,n){if(Ii===null){var a=new Map,l=Ii=new Map;l.set(n,a)}else l=Ii,a=l.get(n),a||(a=new Map,l.set(n,a));if(a.has(e))return a;for(a.set(e,null),n=n.getElementsByTagName(e),l=0;l<n.length;l++){var u=n[l];if(!(u[Ga]||u[Ie]||e==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var d=u.getAttribute(t)||"";d=e+d;var g=a.get(d);g?g.push(u):a.set(d,[u])}}return a}function Kd(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function H0(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Jd(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Dl=null;function B0(){}function q0(e,t,n){if(Dl===null)throw Error(s(475));var a=Dl;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var l=ja(n.href),u=e.querySelector(Ml(l));if(u){e=u._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(a.count++,a=eu.bind(a),e.then(a,a)),t.state.loading|=4,t.instance=u,Qe(u);return}u=e.ownerDocument||e,n=kd(n),(l=_t.get(l))&&Uc(n,l),u=u.createElement("link"),Qe(u);var d=u;d._p=new Promise(function(g,b){d.onload=g,d.onerror=b}),We(u,"link",n),t.instance=u}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(a.count++,t=eu.bind(a),e.addEventListener("load",t),e.addEventListener("error",t))}}function Y0(){if(Dl===null)throw Error(s(475));var e=Dl;return e.stylesheets&&e.count===0&&Cc(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&Cc(e,e.stylesheets),e.unsuspend){var a=e.unsuspend;e.unsuspend=null,a()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function eu(){if(this.count--,this.count===0){if(this.stylesheets)Cc(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var tu=null;function Cc(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,tu=new Map,t.forEach(V0,e),tu=null,eu.call(e))}function V0(e,t){if(!(t.state.loading&4)){var n=tu.get(e);if(n)var a=n.get(null);else{n=new Map,tu.set(e,n);for(var l=e.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<l.length;u++){var d=l[u];(d.nodeName==="LINK"||d.getAttribute("media")!=="not all")&&(n.set(d.dataset.precedence,d),a=d)}a&&n.set(null,a)}l=t.instance,d=l.getAttribute("data-precedence"),u=n.get(d)||a,u===a&&n.set(null,l),n.set(d,l),this.count++,a=eu.bind(this),l.addEventListener("load",a),l.addEventListener("error",a),u?u.parentNode.insertBefore(l,u.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(l,e.firstChild)),t.state.loading|=4}}var zl={$$typeof:F,Provider:null,Consumer:null,_currentValue:Q,_currentValue2:Q,_threadCount:0};function G0(e,t,n,a,l,u,d,g){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=zu(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=zu(0),this.hiddenUpdates=zu(null),this.identifierPrefix=a,this.onUncaughtError=l,this.onCaughtError=u,this.onRecoverableError=d,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=g,this.incompleteTransitions=new Map}function $d(e,t,n,a,l,u,d,g,b,A,U,L){return e=new G0(e,t,n,d,g,b,A,L),t=1,u===!0&&(t|=24),u=mt(3,null,null,t),e.current=u,u.stateNode=e,t=ps(),t.refCount++,e.pooledCache=t,t.refCount++,u.memoizedState={element:a,isDehydrated:n,cache:t},Ss(u),e}function Fd(e){return e?(e=da,e):da}function Wd(e,t,n,a,l,u){l=Fd(l),a.context===null?a.context=l:a.pendingContext=l,a=fn(t),a.payload={element:n},u=u===void 0?null:u,u!==null&&(a.callback=u),n=on(e,a,t),n!==null&&(St(n,e,t),sl(n,e,t))}function Pd(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Lc(e,t){Pd(e,t),(e=e.alternate)&&Pd(e,t)}function Id(e){if(e.tag===13){var t=oa(e,67108864);t!==null&&St(t,e,67108864),Lc(e,67108864)}}var nu=!0;function X0(e,t,n,a){var l=_.T;_.T=null;var u=Y.p;try{Y.p=2,Hc(e,t,n,a)}finally{Y.p=u,_.T=l}}function k0(e,t,n,a){var l=_.T;_.T=null;var u=Y.p;try{Y.p=8,Hc(e,t,n,a)}finally{Y.p=u,_.T=l}}function Hc(e,t,n,a){if(nu){var l=Bc(a);if(l===null)Tc(e,t,a,au,n),th(e,a);else if(Z0(l,e,t,n,a))a.stopPropagation();else if(th(e,a),t&4&&-1<Q0.indexOf(e)){for(;l!==null;){var u=In(l);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var d=_n(u.pendingLanes);if(d!==0){var g=u;for(g.pendingLanes|=2,g.entangledLanes|=2;d;){var b=1<<31-ht(d);g.entanglements[1]|=b,d&=~b}qt(u),(xe&6)===0&&(Yi=jt()+500,Tl(0))}}break;case 13:g=oa(u,2),g!==null&&St(g,u,2),Gi(),Lc(u,2)}if(u=Bc(a),u===null&&Tc(e,t,a,au,n),u===l)break;l=u}l!==null&&a.stopPropagation()}else Tc(e,t,a,null,n)}}function Bc(e){return e=Xu(e),qc(e)}var au=null;function qc(e){if(au=null,e=Pn(e),e!==null){var t=o(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=h(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return au=e,null}function eh(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Dg()){case dr:return 2;case hr:return 8;case $l:case zg:return 32;case gr:return 268435456;default:return 32}default:return 32}}var Yc=!1,Tn=null,An=null,Nn=null,wl=new Map,Ul=new Map,Rn=[],Q0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function th(e,t){switch(e){case"focusin":case"focusout":Tn=null;break;case"dragenter":case"dragleave":An=null;break;case"mouseover":case"mouseout":Nn=null;break;case"pointerover":case"pointerout":wl.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ul.delete(t.pointerId)}}function jl(e,t,n,a,l,u){return e===null||e.nativeEvent!==u?(e={blockedOn:t,domEventName:n,eventSystemFlags:a,nativeEvent:u,targetContainers:[l]},t!==null&&(t=In(t),t!==null&&Id(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function Z0(e,t,n,a,l){switch(t){case"focusin":return Tn=jl(Tn,e,t,n,a,l),!0;case"dragenter":return An=jl(An,e,t,n,a,l),!0;case"mouseover":return Nn=jl(Nn,e,t,n,a,l),!0;case"pointerover":var u=l.pointerId;return wl.set(u,jl(wl.get(u)||null,e,t,n,a,l)),!0;case"gotpointercapture":return u=l.pointerId,Ul.set(u,jl(Ul.get(u)||null,e,t,n,a,l)),!0}return!1}function nh(e){var t=Pn(e.target);if(t!==null){var n=o(t);if(n!==null){if(t=n.tag,t===13){if(t=h(n),t!==null){e.blockedOn=t,qg(e.priority,function(){if(n.tag===13){var a=bt();a=wu(a);var l=oa(n,a);l!==null&&St(l,n,a),Lc(n,a)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function lu(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Bc(e.nativeEvent);if(n===null){n=e.nativeEvent;var a=new n.constructor(n.type,n);Gu=a,n.target.dispatchEvent(a),Gu=null}else return t=In(n),t!==null&&Id(t),e.blockedOn=n,!1;t.shift()}return!0}function ah(e,t,n){lu(e)&&n.delete(t)}function K0(){Yc=!1,Tn!==null&&lu(Tn)&&(Tn=null),An!==null&&lu(An)&&(An=null),Nn!==null&&lu(Nn)&&(Nn=null),wl.forEach(ah),Ul.forEach(ah)}function iu(e,t){e.blockedOn===t&&(e.blockedOn=null,Yc||(Yc=!0,c.unstable_scheduleCallback(c.unstable_NormalPriority,K0)))}var uu=null;function lh(e){uu!==e&&(uu=e,c.unstable_scheduleCallback(c.unstable_NormalPriority,function(){uu===e&&(uu=null);for(var t=0;t<e.length;t+=3){var n=e[t],a=e[t+1],l=e[t+2];if(typeof a!="function"){if(qc(a||n)===null)continue;break}var u=In(n);u!==null&&(e.splice(t,3),t-=3,qs(u,{pending:!0,data:l,method:n.method,action:a},a,l))}}))}function Cl(e){function t(b){return iu(b,e)}Tn!==null&&iu(Tn,e),An!==null&&iu(An,e),Nn!==null&&iu(Nn,e),wl.forEach(t),Ul.forEach(t);for(var n=0;n<Rn.length;n++){var a=Rn[n];a.blockedOn===e&&(a.blockedOn=null)}for(;0<Rn.length&&(n=Rn[0],n.blockedOn===null);)nh(n),n.blockedOn===null&&Rn.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(a=0;a<n.length;a+=3){var l=n[a],u=n[a+1],d=l[lt]||null;if(typeof u=="function")d||lh(n);else if(d){var g=null;if(u&&u.hasAttribute("formAction")){if(l=u,d=u[lt]||null)g=d.formAction;else if(qc(l)!==null)continue}else g=d.action;typeof g=="function"?n[a+1]=g:(n.splice(a,3),a-=3),lh(n)}}}function Vc(e){this._internalRoot=e}su.prototype.render=Vc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(s(409));var n=t.current,a=bt();Wd(n,a,e,t,null,null)},su.prototype.unmount=Vc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Wd(e.current,2,null,e,null,null),Gi(),t[Wn]=null}};function su(e){this._internalRoot=e}su.prototype.unstable_scheduleHydration=function(e){if(e){var t=br();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Rn.length&&t!==0&&t<Rn[n].priority;n++);Rn.splice(n,0,e),n===0&&nh(e)}};var ih=i.version;if(ih!=="19.1.0")throw Error(s(527,ih,"19.1.0"));Y.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(s(188)):(e=Object.keys(e).join(","),Error(s(268,e)));return e=y(t),e=e!==null?m(e):null,e=e===null?null:e.stateNode,e};var J0={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:_,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var cu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!cu.isDisabled&&cu.supportsFiber)try{qa=cu.inject(J0),dt=cu}catch{}}return Hl.createRoot=function(e,t){if(!f(e))throw Error(s(299));var n=!1,a="",l=xo,u=Eo,d=Oo,g=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(a=t.identifierPrefix),t.onUncaughtError!==void 0&&(l=t.onUncaughtError),t.onCaughtError!==void 0&&(u=t.onCaughtError),t.onRecoverableError!==void 0&&(d=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(g=t.unstable_transitionCallbacks)),t=$d(e,1,!1,null,null,n,a,l,u,d,g,null),e[Wn]=t.current,Oc(e),new Vc(t)},Hl.hydrateRoot=function(e,t,n){if(!f(e))throw Error(s(299));var a=!1,l="",u=xo,d=Eo,g=Oo,b=null,A=null;return n!=null&&(n.unstable_strictMode===!0&&(a=!0),n.identifierPrefix!==void 0&&(l=n.identifierPrefix),n.onUncaughtError!==void 0&&(u=n.onUncaughtError),n.onCaughtError!==void 0&&(d=n.onCaughtError),n.onRecoverableError!==void 0&&(g=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(b=n.unstable_transitionCallbacks),n.formState!==void 0&&(A=n.formState)),t=$d(e,1,!0,t,n??null,a,l,u,d,g,b,A),t.context=Fd(null),n=t.current,a=bt(),a=wu(a),l=fn(a),l.callback=null,on(n,l,a),n=a,t.current.lanes=n,Va(t,n),qt(t),e[Wn]=t.current,Oc(e),new su(t)},Hl.version="19.1.0",Hl}var mh;function lp(){if(mh)return kc.exports;mh=1;function c(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(c)}catch(i){console.error(i)}}return c(),kc.exports=ap(),kc.exports}var ip=lp();const up=(c,i,r,s)=>{var o,h,p,y;const f=[r,{code:i,...s||{}}];if((h=(o=c==null?void 0:c.services)==null?void 0:o.logger)!=null&&h.forward)return c.services.logger.forward(f,"warn","react-i18next::",!0);Jn(f[0])&&(f[0]=`react-i18next:: ${f[0]}`),(y=(p=c==null?void 0:c.services)==null?void 0:p.logger)!=null&&y.warn?c.services.logger.warn(...f):console!=null&&console.warn&&console.warn(...f)},ph={},Pc=(c,i,r,s)=>{Jn(r)&&ph[r]||(Jn(r)&&(ph[r]=new Date),up(c,i,r,s))},Jh=(c,i)=>()=>{if(c.isInitialized)i();else{const r=()=>{setTimeout(()=>{c.off("initialized",r)},0),i()};c.on("initialized",r)}},Ic=(c,i,r)=>{c.loadNamespaces(i,Jh(c,r))},yh=(c,i,r,s)=>{if(Jn(r)&&(r=[r]),c.options.preload&&c.options.preload.indexOf(i)>-1)return Ic(c,r,s);r.forEach(f=>{c.options.ns.indexOf(f)<0&&c.options.ns.push(f)}),c.loadLanguages(i,Jh(c,s))},sp=(c,i,r={})=>!i.languages||!i.languages.length?(Pc(i,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:i.languages}),!0):i.hasLoadedNamespace(c,{lng:r.lng,precheck:(s,f)=>{var o;if(((o=r.bindI18n)==null?void 0:o.indexOf("languageChanging"))>-1&&s.services.backendConnector.backend&&s.isLanguageChangingTo&&!f(s.isLanguageChangingTo,c))return!1}}),Jn=c=>typeof c=="string",cp=c=>typeof c=="object"&&c!==null,rp=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,fp={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},op=c=>fp[c],dp=c=>c.replace(rp,op);let er={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:dp};const hp=(c={})=>{er={...er,...c}},gp=()=>er;let $h;const mp=c=>{$h=c},pp=()=>$h,yp={type:"3rdParty",init(c){hp(c.options.react),mp(c)}},Fh=Se.createContext();class vp{constructor(){this.usedNamespaces={}}addUsedNamespaces(i){i.forEach(r=>{this.usedNamespaces[r]||(this.usedNamespaces[r]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const bp=(c,i)=>{const r=Se.useRef();return Se.useEffect(()=>{r.current=c},[c,i]),r.current},Wh=(c,i,r,s)=>c.getFixedT(i,r,s),Sp=(c,i,r,s)=>Se.useCallback(Wh(c,i,r,s),[c,i,r,s]),vu=(c,i={})=>{var le,k,pe,fe;const{i18n:r}=i,{i18n:s,defaultNS:f}=Se.useContext(Fh)||{},o=r||s||pp();if(o&&!o.reportNamespaces&&(o.reportNamespaces=new vp),!o){Pc(o,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const ie=(ge,ze)=>Jn(ze)?ze:cp(ze)&&Jn(ze.defaultValue)?ze.defaultValue:Array.isArray(ge)?ge[ge.length-1]:ge,se=[ie,{},!1];return se.t=ie,se.i18n={},se.ready=!1,se}(le=o.options.react)!=null&&le.wait&&Pc(o,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const h={...gp(),...o.options.react,...i},{useSuspense:p,keyPrefix:y}=h;let m=f||((k=o.options)==null?void 0:k.defaultNS);m=Jn(m)?[m]:m||["translation"],(fe=(pe=o.reportNamespaces).addUsedNamespaces)==null||fe.call(pe,m);const v=(o.isInitialized||o.initializedStoreOnce)&&m.every(ie=>sp(ie,o,h)),E=Sp(o,i.lng||null,h.nsMode==="fallback"?m:m[0],y),w=()=>E,H=()=>Wh(o,i.lng||null,h.nsMode==="fallback"?m:m[0],y),[M,q]=Se.useState(w);let B=m.join();i.lng&&(B=`${i.lng}${B}`);const I=bp(B),$=Se.useRef(!0);Se.useEffect(()=>{const{bindI18n:ie,bindI18nStore:se}=h;$.current=!0,!v&&!p&&(i.lng?yh(o,i.lng,m,()=>{$.current&&q(H)}):Ic(o,m,()=>{$.current&&q(H)})),v&&I&&I!==B&&$.current&&q(H);const ge=()=>{$.current&&q(H)};return ie&&(o==null||o.on(ie,ge)),se&&(o==null||o.store.on(se,ge)),()=>{$.current=!1,o&&(ie==null||ie.split(" ").forEach(ze=>o.off(ze,ge))),se&&o&&se.split(" ").forEach(ze=>o.store.off(ze,ge))}},[o,B]),Se.useEffect(()=>{$.current&&v&&q(w)},[o,y,v]);const F=[M,o,v];if(F.t=M,F.i18n=o,F.ready=v,v||!v&&!p)return F;throw new Promise(ie=>{i.lng?yh(o,i.lng,m,()=>ie()):Ic(o,m,()=>ie())})};function xp({i18n:c,defaultNS:i,children:r}){const s=Se.useMemo(()=>({i18n:c,defaultNS:i}),[c,i]);return Se.createElement(Fh.Provider,{value:s},r)}const te=c=>typeof c=="string",Bl=()=>{let c,i;const r=new Promise((s,f)=>{c=s,i=f});return r.resolve=c,r.reject=i,r},vh=c=>c==null?"":""+c,Ep=(c,i,r)=>{c.forEach(s=>{i[s]&&(r[s]=i[s])})},Op=/###/g,bh=c=>c&&c.indexOf("###")>-1?c.replace(Op,"."):c,Sh=c=>!c||te(c),Gl=(c,i,r)=>{const s=te(i)?i.split("."):i;let f=0;for(;f<s.length-1;){if(Sh(c))return{};const o=bh(s[f]);!c[o]&&r&&(c[o]=new r),Object.prototype.hasOwnProperty.call(c,o)?c=c[o]:c={},++f}return Sh(c)?{}:{obj:c,k:bh(s[f])}},xh=(c,i,r)=>{const{obj:s,k:f}=Gl(c,i,Object);if(s!==void 0||i.length===1){s[f]=r;return}let o=i[i.length-1],h=i.slice(0,i.length-1),p=Gl(c,h,Object);for(;p.obj===void 0&&h.length;)o=`${h[h.length-1]}.${o}`,h=h.slice(0,h.length-1),p=Gl(c,h,Object),p!=null&&p.obj&&typeof p.obj[`${p.k}.${o}`]<"u"&&(p.obj=void 0);p.obj[`${p.k}.${o}`]=r},Tp=(c,i,r,s)=>{const{obj:f,k:o}=Gl(c,i,Object);f[o]=f[o]||[],f[o].push(r)},hu=(c,i)=>{const{obj:r,k:s}=Gl(c,i);if(r&&Object.prototype.hasOwnProperty.call(r,s))return r[s]},Ap=(c,i,r)=>{const s=hu(c,r);return s!==void 0?s:hu(i,r)},Ph=(c,i,r)=>{for(const s in i)s!=="__proto__"&&s!=="constructor"&&(s in c?te(c[s])||c[s]instanceof String||te(i[s])||i[s]instanceof String?r&&(c[s]=i[s]):Ph(c[s],i[s],r):c[s]=i[s]);return c},La=c=>c.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var Np={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const Rp=c=>te(c)?c.replace(/[&<>"'\/]/g,i=>Np[i]):c;class Mp{constructor(i){this.capacity=i,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(i){const r=this.regExpMap.get(i);if(r!==void 0)return r;const s=new RegExp(i);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(i,s),this.regExpQueue.push(i),s}}const _p=[" ",",","?","!",";"],Dp=new Mp(20),zp=(c,i,r)=>{i=i||"",r=r||"";const s=_p.filter(h=>i.indexOf(h)<0&&r.indexOf(h)<0);if(s.length===0)return!0;const f=Dp.getRegExp(`(${s.map(h=>h==="?"?"\\?":h).join("|")})`);let o=!f.test(c);if(!o){const h=c.indexOf(r);h>0&&!f.test(c.substring(0,h))&&(o=!0)}return o},tr=(c,i,r=".")=>{if(!c)return;if(c[i])return Object.prototype.hasOwnProperty.call(c,i)?c[i]:void 0;const s=i.split(r);let f=c;for(let o=0;o<s.length;){if(!f||typeof f!="object")return;let h,p="";for(let y=o;y<s.length;++y)if(y!==o&&(p+=r),p+=s[y],h=f[p],h!==void 0){if(["string","number","boolean"].indexOf(typeof h)>-1&&y<s.length-1)continue;o+=y-o+1;break}f=h}return f},Xl=c=>c==null?void 0:c.replace("_","-"),wp={type:"logger",log(c){this.output("log",c)},warn(c){this.output("warn",c)},error(c){this.output("error",c)},output(c,i){var r,s;(s=(r=console==null?void 0:console[c])==null?void 0:r.apply)==null||s.call(r,console,i)}};class gu{constructor(i,r={}){this.init(i,r)}init(i,r={}){this.prefix=r.prefix||"i18next:",this.logger=i||wp,this.options=r,this.debug=r.debug}log(...i){return this.forward(i,"log","",!0)}warn(...i){return this.forward(i,"warn","",!0)}error(...i){return this.forward(i,"error","")}deprecate(...i){return this.forward(i,"warn","WARNING DEPRECATED: ",!0)}forward(i,r,s,f){return f&&!this.debug?null:(te(i[0])&&(i[0]=`${s}${this.prefix} ${i[0]}`),this.logger[r](i))}create(i){return new gu(this.logger,{prefix:`${this.prefix}:${i}:`,...this.options})}clone(i){return i=i||this.options,i.prefix=i.prefix||this.prefix,new gu(this.logger,i)}}var Vt=new gu;class bu{constructor(){this.observers={}}on(i,r){return i.split(" ").forEach(s=>{this.observers[s]||(this.observers[s]=new Map);const f=this.observers[s].get(r)||0;this.observers[s].set(r,f+1)}),this}off(i,r){if(this.observers[i]){if(!r){delete this.observers[i];return}this.observers[i].delete(r)}}emit(i,...r){this.observers[i]&&Array.from(this.observers[i].entries()).forEach(([f,o])=>{for(let h=0;h<o;h++)f(...r)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(([f,o])=>{for(let h=0;h<o;h++)f.apply(f,[i,...r])})}}class Eh extends bu{constructor(i,r={ns:["translation"],defaultNS:"translation"}){super(),this.data=i||{},this.options=r,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(i){this.options.ns.indexOf(i)<0&&this.options.ns.push(i)}removeNamespaces(i){const r=this.options.ns.indexOf(i);r>-1&&this.options.ns.splice(r,1)}getResource(i,r,s,f={}){var m,v;const o=f.keySeparator!==void 0?f.keySeparator:this.options.keySeparator,h=f.ignoreJSONStructure!==void 0?f.ignoreJSONStructure:this.options.ignoreJSONStructure;let p;i.indexOf(".")>-1?p=i.split("."):(p=[i,r],s&&(Array.isArray(s)?p.push(...s):te(s)&&o?p.push(...s.split(o)):p.push(s)));const y=hu(this.data,p);return!y&&!r&&!s&&i.indexOf(".")>-1&&(i=p[0],r=p[1],s=p.slice(2).join(".")),y||!h||!te(s)?y:tr((v=(m=this.data)==null?void 0:m[i])==null?void 0:v[r],s,o)}addResource(i,r,s,f,o={silent:!1}){const h=o.keySeparator!==void 0?o.keySeparator:this.options.keySeparator;let p=[i,r];s&&(p=p.concat(h?s.split(h):s)),i.indexOf(".")>-1&&(p=i.split("."),f=r,r=p[1]),this.addNamespaces(r),xh(this.data,p,f),o.silent||this.emit("added",i,r,s,f)}addResources(i,r,s,f={silent:!1}){for(const o in s)(te(s[o])||Array.isArray(s[o]))&&this.addResource(i,r,o,s[o],{silent:!0});f.silent||this.emit("added",i,r,s)}addResourceBundle(i,r,s,f,o,h={silent:!1,skipCopy:!1}){let p=[i,r];i.indexOf(".")>-1&&(p=i.split("."),f=s,s=r,r=p[1]),this.addNamespaces(r);let y=hu(this.data,p)||{};h.skipCopy||(s=JSON.parse(JSON.stringify(s))),f?Ph(y,s,o):y={...y,...s},xh(this.data,p,y),h.silent||this.emit("added",i,r,s)}removeResourceBundle(i,r){this.hasResourceBundle(i,r)&&delete this.data[i][r],this.removeNamespaces(r),this.emit("removed",i,r)}hasResourceBundle(i,r){return this.getResource(i,r)!==void 0}getResourceBundle(i,r){return r||(r=this.options.defaultNS),this.getResource(i,r)}getDataByLanguage(i){return this.data[i]}hasLanguageSomeTranslations(i){const r=this.getDataByLanguage(i);return!!(r&&Object.keys(r)||[]).find(f=>r[f]&&Object.keys(r[f]).length>0)}toJSON(){return this.data}}var Ih={processors:{},addPostProcessor(c){this.processors[c.name]=c},handle(c,i,r,s,f){return c.forEach(o=>{var h;i=((h=this.processors[o])==null?void 0:h.process(i,r,s,f))??i}),i}};const Oh={},Th=c=>!te(c)&&typeof c!="boolean"&&typeof c!="number";class mu extends bu{constructor(i,r={}){super(),Ep(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],i,this),this.options=r,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=Vt.create("translator")}changeLanguage(i){i&&(this.language=i)}exists(i,r={interpolation:{}}){const s={...r};if(i==null)return!1;const f=this.resolve(i,s);return(f==null?void 0:f.res)!==void 0}extractFromKey(i,r){let s=r.nsSeparator!==void 0?r.nsSeparator:this.options.nsSeparator;s===void 0&&(s=":");const f=r.keySeparator!==void 0?r.keySeparator:this.options.keySeparator;let o=r.ns||this.options.defaultNS||[];const h=s&&i.indexOf(s)>-1,p=!this.options.userDefinedKeySeparator&&!r.keySeparator&&!this.options.userDefinedNsSeparator&&!r.nsSeparator&&!zp(i,s,f);if(h&&!p){const y=i.match(this.interpolator.nestingRegexp);if(y&&y.length>0)return{key:i,namespaces:te(o)?[o]:o};const m=i.split(s);(s!==f||s===f&&this.options.ns.indexOf(m[0])>-1)&&(o=m.shift()),i=m.join(f)}return{key:i,namespaces:te(o)?[o]:o}}translate(i,r,s){let f=typeof r=="object"?{...r}:r;if(typeof f!="object"&&this.options.overloadTranslationOptionHandler&&(f=this.options.overloadTranslationOptionHandler(arguments)),typeof options=="object"&&(f={...f}),f||(f={}),i==null)return"";Array.isArray(i)||(i=[String(i)]);const o=f.returnDetails!==void 0?f.returnDetails:this.options.returnDetails,h=f.keySeparator!==void 0?f.keySeparator:this.options.keySeparator,{key:p,namespaces:y}=this.extractFromKey(i[i.length-1],f),m=y[y.length-1];let v=f.nsSeparator!==void 0?f.nsSeparator:this.options.nsSeparator;v===void 0&&(v=":");const E=f.lng||this.language,w=f.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if((E==null?void 0:E.toLowerCase())==="cimode")return w?o?{res:`${m}${v}${p}`,usedKey:p,exactUsedKey:p,usedLng:E,usedNS:m,usedParams:this.getUsedParamsDetails(f)}:`${m}${v}${p}`:o?{res:p,usedKey:p,exactUsedKey:p,usedLng:E,usedNS:m,usedParams:this.getUsedParamsDetails(f)}:p;const H=this.resolve(i,f);let M=H==null?void 0:H.res;const q=(H==null?void 0:H.usedKey)||p,B=(H==null?void 0:H.exactUsedKey)||p,I=["[object Number]","[object Function]","[object RegExp]"],$=f.joinArrays!==void 0?f.joinArrays:this.options.joinArrays,F=!this.i18nFormat||this.i18nFormat.handleAsObject,le=f.count!==void 0&&!te(f.count),k=mu.hasDefaultValue(f),pe=le?this.pluralResolver.getSuffix(E,f.count,f):"",fe=f.ordinal&&le?this.pluralResolver.getSuffix(E,f.count,{ordinal:!1}):"",ie=le&&!f.ordinal&&f.count===0,se=ie&&f[`defaultValue${this.options.pluralSeparator}zero`]||f[`defaultValue${pe}`]||f[`defaultValue${fe}`]||f.defaultValue;let ge=M;F&&!M&&k&&(ge=se);const ze=Th(ge),Be=Object.prototype.toString.apply(ge);if(F&&ge&&ze&&I.indexOf(Be)<0&&!(te($)&&Array.isArray(ge))){if(!f.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const qe=this.options.returnedObjectHandler?this.options.returnedObjectHandler(q,ge,{...f,ns:y}):`key '${p} (${this.language})' returned an object instead of string.`;return o?(H.res=qe,H.usedParams=this.getUsedParamsDetails(f),H):qe}if(h){const qe=Array.isArray(ge),K=qe?[]:{},Me=qe?B:q;for(const _ in ge)if(Object.prototype.hasOwnProperty.call(ge,_)){const Y=`${Me}${h}${_}`;k&&!M?K[_]=this.translate(Y,{...f,defaultValue:Th(se)?se[_]:void 0,joinArrays:!1,ns:y}):K[_]=this.translate(Y,{...f,joinArrays:!1,ns:y}),K[_]===Y&&(K[_]=ge[_])}M=K}}else if(F&&te($)&&Array.isArray(M))M=M.join($),M&&(M=this.extendTranslation(M,i,f,s));else{let qe=!1,K=!1;!this.isValidLookup(M)&&k&&(qe=!0,M=se),this.isValidLookup(M)||(K=!0,M=p);const _=(f.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&K?void 0:M,Y=k&&se!==M&&this.options.updateMissing;if(K||qe||Y){if(this.logger.log(Y?"updateKey":"missingKey",E,m,p,Y?se:M),h){const C=this.resolve(p,{...f,keySeparator:!1});C&&C.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let Q=[];const ye=this.languageUtils.getFallbackCodes(this.options.fallbackLng,f.lng||this.language);if(this.options.saveMissingTo==="fallback"&&ye&&ye[0])for(let C=0;C<ye.length;C++)Q.push(ye[C]);else this.options.saveMissingTo==="all"?Q=this.languageUtils.toResolveHierarchy(f.lng||this.language):Q.push(f.lng||this.language);const S=(C,G,V)=>{var he;const J=k&&V!==M?V:_;this.options.missingKeyHandler?this.options.missingKeyHandler(C,m,G,J,Y,f):(he=this.backendConnector)!=null&&he.saveMissing&&this.backendConnector.saveMissing(C,m,G,J,Y,f),this.emit("missingKey",C,m,G,M)};this.options.saveMissing&&(this.options.saveMissingPlurals&&le?Q.forEach(C=>{const G=this.pluralResolver.getSuffixes(C,f);ie&&f[`defaultValue${this.options.pluralSeparator}zero`]&&G.indexOf(`${this.options.pluralSeparator}zero`)<0&&G.push(`${this.options.pluralSeparator}zero`),G.forEach(V=>{S([C],p+V,f[`defaultValue${V}`]||se)})}):S(Q,p,se))}M=this.extendTranslation(M,i,f,H,s),K&&M===p&&this.options.appendNamespaceToMissingKey&&(M=`${m}${v}${p}`),(K||qe)&&this.options.parseMissingKeyHandler&&(M=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${m}${v}${p}`:p,qe?M:void 0,f))}return o?(H.res=M,H.usedParams=this.getUsedParamsDetails(f),H):M}extendTranslation(i,r,s,f,o){var y,m;if((y=this.i18nFormat)!=null&&y.parse)i=this.i18nFormat.parse(i,{...this.options.interpolation.defaultVariables,...s},s.lng||this.language||f.usedLng,f.usedNS,f.usedKey,{resolved:f});else if(!s.skipInterpolation){s.interpolation&&this.interpolator.init({...s,interpolation:{...this.options.interpolation,...s.interpolation}});const v=te(i)&&(((m=s==null?void 0:s.interpolation)==null?void 0:m.skipOnVariables)!==void 0?s.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let E;if(v){const H=i.match(this.interpolator.nestingRegexp);E=H&&H.length}let w=s.replace&&!te(s.replace)?s.replace:s;if(this.options.interpolation.defaultVariables&&(w={...this.options.interpolation.defaultVariables,...w}),i=this.interpolator.interpolate(i,w,s.lng||this.language||f.usedLng,s),v){const H=i.match(this.interpolator.nestingRegexp),M=H&&H.length;E<M&&(s.nest=!1)}!s.lng&&f&&f.res&&(s.lng=this.language||f.usedLng),s.nest!==!1&&(i=this.interpolator.nest(i,(...H)=>(o==null?void 0:o[0])===H[0]&&!s.context?(this.logger.warn(`It seems you are nesting recursively key: ${H[0]} in key: ${r[0]}`),null):this.translate(...H,r),s)),s.interpolation&&this.interpolator.reset()}const h=s.postProcess||this.options.postProcess,p=te(h)?[h]:h;return i!=null&&(p!=null&&p.length)&&s.applyPostProcessor!==!1&&(i=Ih.handle(p,i,r,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...f,usedParams:this.getUsedParamsDetails(s)},...s}:s,this)),i}resolve(i,r={}){let s,f,o,h,p;return te(i)&&(i=[i]),i.forEach(y=>{if(this.isValidLookup(s))return;const m=this.extractFromKey(y,r),v=m.key;f=v;let E=m.namespaces;this.options.fallbackNS&&(E=E.concat(this.options.fallbackNS));const w=r.count!==void 0&&!te(r.count),H=w&&!r.ordinal&&r.count===0,M=r.context!==void 0&&(te(r.context)||typeof r.context=="number")&&r.context!=="",q=r.lngs?r.lngs:this.languageUtils.toResolveHierarchy(r.lng||this.language,r.fallbackLng);E.forEach(B=>{var I,$;this.isValidLookup(s)||(p=B,!Oh[`${q[0]}-${B}`]&&((I=this.utils)!=null&&I.hasLoadedNamespace)&&!(($=this.utils)!=null&&$.hasLoadedNamespace(p))&&(Oh[`${q[0]}-${B}`]=!0,this.logger.warn(`key "${f}" for languages "${q.join(", ")}" won't get resolved as namespace "${p}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),q.forEach(F=>{var pe;if(this.isValidLookup(s))return;h=F;const le=[v];if((pe=this.i18nFormat)!=null&&pe.addLookupKeys)this.i18nFormat.addLookupKeys(le,v,F,B,r);else{let fe;w&&(fe=this.pluralResolver.getSuffix(F,r.count,r));const ie=`${this.options.pluralSeparator}zero`,se=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(w&&(le.push(v+fe),r.ordinal&&fe.indexOf(se)===0&&le.push(v+fe.replace(se,this.options.pluralSeparator)),H&&le.push(v+ie)),M){const ge=`${v}${this.options.contextSeparator}${r.context}`;le.push(ge),w&&(le.push(ge+fe),r.ordinal&&fe.indexOf(se)===0&&le.push(ge+fe.replace(se,this.options.pluralSeparator)),H&&le.push(ge+ie))}}let k;for(;k=le.pop();)this.isValidLookup(s)||(o=k,s=this.getResource(F,B,k,r))}))})}),{res:s,usedKey:f,exactUsedKey:o,usedLng:h,usedNS:p}}isValidLookup(i){return i!==void 0&&!(!this.options.returnNull&&i===null)&&!(!this.options.returnEmptyString&&i==="")}getResource(i,r,s,f={}){var o;return(o=this.i18nFormat)!=null&&o.getResource?this.i18nFormat.getResource(i,r,s,f):this.resourceStore.getResource(i,r,s,f)}getUsedParamsDetails(i={}){const r=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],s=i.replace&&!te(i.replace);let f=s?i.replace:i;if(s&&typeof i.count<"u"&&(f.count=i.count),this.options.interpolation.defaultVariables&&(f={...this.options.interpolation.defaultVariables,...f}),!s){f={...f};for(const o of r)delete f[o]}return f}static hasDefaultValue(i){const r="defaultValue";for(const s in i)if(Object.prototype.hasOwnProperty.call(i,s)&&r===s.substring(0,r.length)&&i[s]!==void 0)return!0;return!1}}class Ah{constructor(i){this.options=i,this.supportedLngs=this.options.supportedLngs||!1,this.logger=Vt.create("languageUtils")}getScriptPartFromCode(i){if(i=Xl(i),!i||i.indexOf("-")<0)return null;const r=i.split("-");return r.length===2||(r.pop(),r[r.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(r.join("-"))}getLanguagePartFromCode(i){if(i=Xl(i),!i||i.indexOf("-")<0)return i;const r=i.split("-");return this.formatLanguageCode(r[0])}formatLanguageCode(i){if(te(i)&&i.indexOf("-")>-1){let r;try{r=Intl.getCanonicalLocales(i)[0]}catch{}return r&&this.options.lowerCaseLng&&(r=r.toLowerCase()),r||(this.options.lowerCaseLng?i.toLowerCase():i)}return this.options.cleanCode||this.options.lowerCaseLng?i.toLowerCase():i}isSupportedCode(i){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(i=this.getLanguagePartFromCode(i)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(i)>-1}getBestMatchFromCodes(i){if(!i)return null;let r;return i.forEach(s=>{if(r)return;const f=this.formatLanguageCode(s);(!this.options.supportedLngs||this.isSupportedCode(f))&&(r=f)}),!r&&this.options.supportedLngs&&i.forEach(s=>{if(r)return;const f=this.getScriptPartFromCode(s);if(this.isSupportedCode(f))return r=f;const o=this.getLanguagePartFromCode(s);if(this.isSupportedCode(o))return r=o;r=this.options.supportedLngs.find(h=>{if(h===o)return h;if(!(h.indexOf("-")<0&&o.indexOf("-")<0)&&(h.indexOf("-")>0&&o.indexOf("-")<0&&h.substring(0,h.indexOf("-"))===o||h.indexOf(o)===0&&o.length>1))return h})}),r||(r=this.getFallbackCodes(this.options.fallbackLng)[0]),r}getFallbackCodes(i,r){if(!i)return[];if(typeof i=="function"&&(i=i(r)),te(i)&&(i=[i]),Array.isArray(i))return i;if(!r)return i.default||[];let s=i[r];return s||(s=i[this.getScriptPartFromCode(r)]),s||(s=i[this.formatLanguageCode(r)]),s||(s=i[this.getLanguagePartFromCode(r)]),s||(s=i.default),s||[]}toResolveHierarchy(i,r){const s=this.getFallbackCodes((r===!1?[]:r)||this.options.fallbackLng||[],i),f=[],o=h=>{h&&(this.isSupportedCode(h)?f.push(h):this.logger.warn(`rejecting language code not found in supportedLngs: ${h}`))};return te(i)&&(i.indexOf("-")>-1||i.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&o(this.formatLanguageCode(i)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&o(this.getScriptPartFromCode(i)),this.options.load!=="currentOnly"&&o(this.getLanguagePartFromCode(i))):te(i)&&o(this.formatLanguageCode(i)),s.forEach(h=>{f.indexOf(h)<0&&o(this.formatLanguageCode(h))}),f}}const Nh={zero:0,one:1,two:2,few:3,many:4,other:5},Rh={select:c=>c===1?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class Up{constructor(i,r={}){this.languageUtils=i,this.options=r,this.logger=Vt.create("pluralResolver"),this.pluralRulesCache={}}addRule(i,r){this.rules[i]=r}clearCache(){this.pluralRulesCache={}}getRule(i,r={}){const s=Xl(i==="dev"?"en":i),f=r.ordinal?"ordinal":"cardinal",o=JSON.stringify({cleanedCode:s,type:f});if(o in this.pluralRulesCache)return this.pluralRulesCache[o];let h;try{h=new Intl.PluralRules(s,{type:f})}catch{if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),Rh;if(!i.match(/-|_/))return Rh;const y=this.languageUtils.getLanguagePartFromCode(i);h=this.getRule(y,r)}return this.pluralRulesCache[o]=h,h}needsPlural(i,r={}){let s=this.getRule(i,r);return s||(s=this.getRule("dev",r)),(s==null?void 0:s.resolvedOptions().pluralCategories.length)>1}getPluralFormsOfKey(i,r,s={}){return this.getSuffixes(i,s).map(f=>`${r}${f}`)}getSuffixes(i,r={}){let s=this.getRule(i,r);return s||(s=this.getRule("dev",r)),s?s.resolvedOptions().pluralCategories.sort((f,o)=>Nh[f]-Nh[o]).map(f=>`${this.options.prepend}${r.ordinal?`ordinal${this.options.prepend}`:""}${f}`):[]}getSuffix(i,r,s={}){const f=this.getRule(i,s);return f?`${this.options.prepend}${s.ordinal?`ordinal${this.options.prepend}`:""}${f.select(r)}`:(this.logger.warn(`no plural rule found for: ${i}`),this.getSuffix("dev",r,s))}}const Mh=(c,i,r,s=".",f=!0)=>{let o=Ap(c,i,r);return!o&&f&&te(r)&&(o=tr(c,r,s),o===void 0&&(o=tr(i,r,s))),o},Jc=c=>c.replace(/\$/g,"$$$$");class jp{constructor(i={}){var r;this.logger=Vt.create("interpolator"),this.options=i,this.format=((r=i==null?void 0:i.interpolation)==null?void 0:r.format)||(s=>s),this.init(i)}init(i={}){i.interpolation||(i.interpolation={escapeValue:!0});const{escape:r,escapeValue:s,useRawValueToEscape:f,prefix:o,prefixEscaped:h,suffix:p,suffixEscaped:y,formatSeparator:m,unescapeSuffix:v,unescapePrefix:E,nestingPrefix:w,nestingPrefixEscaped:H,nestingSuffix:M,nestingSuffixEscaped:q,nestingOptionsSeparator:B,maxReplaces:I,alwaysFormat:$}=i.interpolation;this.escape=r!==void 0?r:Rp,this.escapeValue=s!==void 0?s:!0,this.useRawValueToEscape=f!==void 0?f:!1,this.prefix=o?La(o):h||"{{",this.suffix=p?La(p):y||"}}",this.formatSeparator=m||",",this.unescapePrefix=v?"":E||"-",this.unescapeSuffix=this.unescapePrefix?"":v||"",this.nestingPrefix=w?La(w):H||La("$t("),this.nestingSuffix=M?La(M):q||La(")"),this.nestingOptionsSeparator=B||",",this.maxReplaces=I||1e3,this.alwaysFormat=$!==void 0?$:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const i=(r,s)=>(r==null?void 0:r.source)===s?(r.lastIndex=0,r):new RegExp(s,"g");this.regexp=i(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=i(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=i(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(i,r,s,f){var H;let o,h,p;const y=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},m=M=>{if(M.indexOf(this.formatSeparator)<0){const $=Mh(r,y,M,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format($,void 0,s,{...f,...r,interpolationkey:M}):$}const q=M.split(this.formatSeparator),B=q.shift().trim(),I=q.join(this.formatSeparator).trim();return this.format(Mh(r,y,B,this.options.keySeparator,this.options.ignoreJSONStructure),I,s,{...f,...r,interpolationkey:B})};this.resetRegExp();const v=(f==null?void 0:f.missingInterpolationHandler)||this.options.missingInterpolationHandler,E=((H=f==null?void 0:f.interpolation)==null?void 0:H.skipOnVariables)!==void 0?f.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:M=>Jc(M)},{regex:this.regexp,safeValue:M=>this.escapeValue?Jc(this.escape(M)):Jc(M)}].forEach(M=>{for(p=0;o=M.regex.exec(i);){const q=o[1].trim();if(h=m(q),h===void 0)if(typeof v=="function"){const I=v(i,o,f);h=te(I)?I:""}else if(f&&Object.prototype.hasOwnProperty.call(f,q))h="";else if(E){h=o[0];continue}else this.logger.warn(`missed to pass in variable ${q} for interpolating ${i}`),h="";else!te(h)&&!this.useRawValueToEscape&&(h=vh(h));const B=M.safeValue(h);if(i=i.replace(o[0],B),E?(M.regex.lastIndex+=h.length,M.regex.lastIndex-=o[0].length):M.regex.lastIndex=0,p++,p>=this.maxReplaces)break}}),i}nest(i,r,s={}){let f,o,h;const p=(y,m)=>{const v=this.nestingOptionsSeparator;if(y.indexOf(v)<0)return y;const E=y.split(new RegExp(`${v}[ ]*{`));let w=`{${E[1]}`;y=E[0],w=this.interpolate(w,h);const H=w.match(/'/g),M=w.match(/"/g);(((H==null?void 0:H.length)??0)%2===0&&!M||M.length%2!==0)&&(w=w.replace(/'/g,'"'));try{h=JSON.parse(w),m&&(h={...m,...h})}catch(q){return this.logger.warn(`failed parsing options string in nesting for key ${y}`,q),`${y}${v}${w}`}return h.defaultValue&&h.defaultValue.indexOf(this.prefix)>-1&&delete h.defaultValue,y};for(;f=this.nestingRegexp.exec(i);){let y=[];h={...s},h=h.replace&&!te(h.replace)?h.replace:h,h.applyPostProcessor=!1,delete h.defaultValue;let m=!1;if(f[0].indexOf(this.formatSeparator)!==-1&&!/{.*}/.test(f[1])){const v=f[1].split(this.formatSeparator).map(E=>E.trim());f[1]=v.shift(),y=v,m=!0}if(o=r(p.call(this,f[1].trim(),h),h),o&&f[0]===i&&!te(o))return o;te(o)||(o=vh(o)),o||(this.logger.warn(`missed to resolve ${f[1]} for nesting ${i}`),o=""),m&&(o=y.reduce((v,E)=>this.format(v,E,s.lng,{...s,interpolationkey:f[1].trim()}),o.trim())),i=i.replace(f[0],o),this.regexp.lastIndex=0}return i}}const Cp=c=>{let i=c.toLowerCase().trim();const r={};if(c.indexOf("(")>-1){const s=c.split("(");i=s[0].toLowerCase().trim();const f=s[1].substring(0,s[1].length-1);i==="currency"&&f.indexOf(":")<0?r.currency||(r.currency=f.trim()):i==="relativetime"&&f.indexOf(":")<0?r.range||(r.range=f.trim()):f.split(";").forEach(h=>{if(h){const[p,...y]=h.split(":"),m=y.join(":").trim().replace(/^'+|'+$/g,""),v=p.trim();r[v]||(r[v]=m),m==="false"&&(r[v]=!1),m==="true"&&(r[v]=!0),isNaN(m)||(r[v]=parseInt(m,10))}})}return{formatName:i,formatOptions:r}},_h=c=>{const i={};return(r,s,f)=>{let o=f;f&&f.interpolationkey&&f.formatParams&&f.formatParams[f.interpolationkey]&&f[f.interpolationkey]&&(o={...o,[f.interpolationkey]:void 0});const h=s+JSON.stringify(o);let p=i[h];return p||(p=c(Xl(s),f),i[h]=p),p(r)}},Lp=c=>(i,r,s)=>c(Xl(r),s)(i);class Hp{constructor(i={}){this.logger=Vt.create("formatter"),this.options=i,this.init(i)}init(i,r={interpolation:{}}){this.formatSeparator=r.interpolation.formatSeparator||",";const s=r.cacheInBuiltFormats?_h:Lp;this.formats={number:s((f,o)=>{const h=new Intl.NumberFormat(f,{...o});return p=>h.format(p)}),currency:s((f,o)=>{const h=new Intl.NumberFormat(f,{...o,style:"currency"});return p=>h.format(p)}),datetime:s((f,o)=>{const h=new Intl.DateTimeFormat(f,{...o});return p=>h.format(p)}),relativetime:s((f,o)=>{const h=new Intl.RelativeTimeFormat(f,{...o});return p=>h.format(p,o.range||"day")}),list:s((f,o)=>{const h=new Intl.ListFormat(f,{...o});return p=>h.format(p)})}}add(i,r){this.formats[i.toLowerCase().trim()]=r}addCached(i,r){this.formats[i.toLowerCase().trim()]=_h(r)}format(i,r,s,f={}){const o=r.split(this.formatSeparator);if(o.length>1&&o[0].indexOf("(")>1&&o[0].indexOf(")")<0&&o.find(p=>p.indexOf(")")>-1)){const p=o.findIndex(y=>y.indexOf(")")>-1);o[0]=[o[0],...o.splice(1,p)].join(this.formatSeparator)}return o.reduce((p,y)=>{var E;const{formatName:m,formatOptions:v}=Cp(y);if(this.formats[m]){let w=p;try{const H=((E=f==null?void 0:f.formatParams)==null?void 0:E[f.interpolationkey])||{},M=H.locale||H.lng||f.locale||f.lng||s;w=this.formats[m](p,M,{...v,...f,...H})}catch(H){this.logger.warn(H)}return w}else this.logger.warn(`there was no format function for ${m}`);return p},i)}}const Bp=(c,i)=>{c.pending[i]!==void 0&&(delete c.pending[i],c.pendingCount--)};class qp extends bu{constructor(i,r,s,f={}){var o,h;super(),this.backend=i,this.store=r,this.services=s,this.languageUtils=s.languageUtils,this.options=f,this.logger=Vt.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=f.maxParallelReads||10,this.readingCalls=0,this.maxRetries=f.maxRetries>=0?f.maxRetries:5,this.retryTimeout=f.retryTimeout>=1?f.retryTimeout:350,this.state={},this.queue=[],(h=(o=this.backend)==null?void 0:o.init)==null||h.call(o,s,f.backend,f)}queueLoad(i,r,s,f){const o={},h={},p={},y={};return i.forEach(m=>{let v=!0;r.forEach(E=>{const w=`${m}|${E}`;!s.reload&&this.store.hasResourceBundle(m,E)?this.state[w]=2:this.state[w]<0||(this.state[w]===1?h[w]===void 0&&(h[w]=!0):(this.state[w]=1,v=!1,h[w]===void 0&&(h[w]=!0),o[w]===void 0&&(o[w]=!0),y[E]===void 0&&(y[E]=!0)))}),v||(p[m]=!0)}),(Object.keys(o).length||Object.keys(h).length)&&this.queue.push({pending:h,pendingCount:Object.keys(h).length,loaded:{},errors:[],callback:f}),{toLoad:Object.keys(o),pending:Object.keys(h),toLoadLanguages:Object.keys(p),toLoadNamespaces:Object.keys(y)}}loaded(i,r,s){const f=i.split("|"),o=f[0],h=f[1];r&&this.emit("failedLoading",o,h,r),!r&&s&&this.store.addResourceBundle(o,h,s,void 0,void 0,{skipCopy:!0}),this.state[i]=r?-1:2,r&&s&&(this.state[i]=0);const p={};this.queue.forEach(y=>{Tp(y.loaded,[o],h),Bp(y,i),r&&y.errors.push(r),y.pendingCount===0&&!y.done&&(Object.keys(y.loaded).forEach(m=>{p[m]||(p[m]={});const v=y.loaded[m];v.length&&v.forEach(E=>{p[m][E]===void 0&&(p[m][E]=!0)})}),y.done=!0,y.errors.length?y.callback(y.errors):y.callback())}),this.emit("loaded",p),this.queue=this.queue.filter(y=>!y.done)}read(i,r,s,f=0,o=this.retryTimeout,h){if(!i.length)return h(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:i,ns:r,fcName:s,tried:f,wait:o,callback:h});return}this.readingCalls++;const p=(m,v)=>{if(this.readingCalls--,this.waitingReads.length>0){const E=this.waitingReads.shift();this.read(E.lng,E.ns,E.fcName,E.tried,E.wait,E.callback)}if(m&&v&&f<this.maxRetries){setTimeout(()=>{this.read.call(this,i,r,s,f+1,o*2,h)},o);return}h(m,v)},y=this.backend[s].bind(this.backend);if(y.length===2){try{const m=y(i,r);m&&typeof m.then=="function"?m.then(v=>p(null,v)).catch(p):p(null,m)}catch(m){p(m)}return}return y(i,r,p)}prepareLoading(i,r,s={},f){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),f&&f();te(i)&&(i=this.languageUtils.toResolveHierarchy(i)),te(r)&&(r=[r]);const o=this.queueLoad(i,r,s,f);if(!o.toLoad.length)return o.pending.length||f(),null;o.toLoad.forEach(h=>{this.loadOne(h)})}load(i,r,s){this.prepareLoading(i,r,{},s)}reload(i,r,s){this.prepareLoading(i,r,{reload:!0},s)}loadOne(i,r=""){const s=i.split("|"),f=s[0],o=s[1];this.read(f,o,"read",void 0,void 0,(h,p)=>{h&&this.logger.warn(`${r}loading namespace ${o} for language ${f} failed`,h),!h&&p&&this.logger.log(`${r}loaded namespace ${o} for language ${f}`,p),this.loaded(i,h,p)})}saveMissing(i,r,s,f,o,h={},p=()=>{}){var y,m,v,E,w;if((m=(y=this.services)==null?void 0:y.utils)!=null&&m.hasLoadedNamespace&&!((E=(v=this.services)==null?void 0:v.utils)!=null&&E.hasLoadedNamespace(r))){this.logger.warn(`did not save key "${s}" as the namespace "${r}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(s==null||s==="")){if((w=this.backend)!=null&&w.create){const H={...h,isUpdate:o},M=this.backend.create.bind(this.backend);if(M.length<6)try{let q;M.length===5?q=M(i,r,s,f,H):q=M(i,r,s,f),q&&typeof q.then=="function"?q.then(B=>p(null,B)).catch(p):p(null,q)}catch(q){p(q)}else M(i,r,s,f,p,H)}!i||!i[0]||this.store.addResource(i[0],r,s,f)}}}const Dh=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:c=>{let i={};if(typeof c[1]=="object"&&(i=c[1]),te(c[1])&&(i.defaultValue=c[1]),te(c[2])&&(i.tDescription=c[2]),typeof c[2]=="object"||typeof c[3]=="object"){const r=c[3]||c[2];Object.keys(r).forEach(s=>{i[s]=r[s]})}return i},interpolation:{escapeValue:!0,format:c=>c,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),zh=c=>{var i,r;return te(c.ns)&&(c.ns=[c.ns]),te(c.fallbackLng)&&(c.fallbackLng=[c.fallbackLng]),te(c.fallbackNS)&&(c.fallbackNS=[c.fallbackNS]),((r=(i=c.supportedLngs)==null?void 0:i.indexOf)==null?void 0:r.call(i,"cimode"))<0&&(c.supportedLngs=c.supportedLngs.concat(["cimode"])),typeof c.initImmediate=="boolean"&&(c.initAsync=c.initImmediate),c},ru=()=>{},Yp=c=>{Object.getOwnPropertyNames(Object.getPrototypeOf(c)).forEach(r=>{typeof c[r]=="function"&&(c[r]=c[r].bind(c))})};class kl extends bu{constructor(i={},r){if(super(),this.options=zh(i),this.services={},this.logger=Vt,this.modules={external:[]},Yp(this),r&&!this.isInitialized&&!i.isClone){if(!this.options.initAsync)return this.init(i,r),this;setTimeout(()=>{this.init(i,r)},0)}}init(i={},r){this.isInitializing=!0,typeof i=="function"&&(r=i,i={}),i.defaultNS==null&&i.ns&&(te(i.ns)?i.defaultNS=i.ns:i.ns.indexOf("translation")<0&&(i.defaultNS=i.ns[0]));const s=Dh();this.options={...s,...this.options,...zh(i)},this.options.interpolation={...s.interpolation,...this.options.interpolation},i.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=i.keySeparator),i.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=i.nsSeparator);const f=m=>m?typeof m=="function"?new m:m:null;if(!this.options.isClone){this.modules.logger?Vt.init(f(this.modules.logger),this.options):Vt.init(null,this.options);let m;this.modules.formatter?m=this.modules.formatter:m=Hp;const v=new Ah(this.options);this.store=new Eh(this.options.resources,this.options);const E=this.services;E.logger=Vt,E.resourceStore=this.store,E.languageUtils=v,E.pluralResolver=new Up(v,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),m&&(!this.options.interpolation.format||this.options.interpolation.format===s.interpolation.format)&&(E.formatter=f(m),E.formatter.init(E,this.options),this.options.interpolation.format=E.formatter.format.bind(E.formatter)),E.interpolator=new jp(this.options),E.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},E.backendConnector=new qp(f(this.modules.backend),E.resourceStore,E,this.options),E.backendConnector.on("*",(w,...H)=>{this.emit(w,...H)}),this.modules.languageDetector&&(E.languageDetector=f(this.modules.languageDetector),E.languageDetector.init&&E.languageDetector.init(E,this.options.detection,this.options)),this.modules.i18nFormat&&(E.i18nFormat=f(this.modules.i18nFormat),E.i18nFormat.init&&E.i18nFormat.init(this)),this.translator=new mu(this.services,this.options),this.translator.on("*",(w,...H)=>{this.emit(w,...H)}),this.modules.external.forEach(w=>{w.init&&w.init(this)})}if(this.format=this.options.interpolation.format,r||(r=ru),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const m=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);m.length>0&&m[0]!=="dev"&&(this.options.lng=m[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(m=>{this[m]=(...v)=>this.store[m](...v)}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(m=>{this[m]=(...v)=>(this.store[m](...v),this)});const p=Bl(),y=()=>{const m=(v,E)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),p.resolve(E),r(v,E)};if(this.languages&&!this.isInitialized)return m(null,this.t.bind(this));this.changeLanguage(this.options.lng,m)};return this.options.resources||!this.options.initAsync?y():setTimeout(y,0),p}loadResources(i,r=ru){var o,h;let s=r;const f=te(i)?i:this.language;if(typeof i=="function"&&(s=i),!this.options.resources||this.options.partialBundledLanguages){if((f==null?void 0:f.toLowerCase())==="cimode"&&(!this.options.preload||this.options.preload.length===0))return s();const p=[],y=m=>{if(!m||m==="cimode")return;this.services.languageUtils.toResolveHierarchy(m).forEach(E=>{E!=="cimode"&&p.indexOf(E)<0&&p.push(E)})};f?y(f):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(v=>y(v)),(h=(o=this.options.preload)==null?void 0:o.forEach)==null||h.call(o,m=>y(m)),this.services.backendConnector.load(p,this.options.ns,m=>{!m&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),s(m)})}else s(null)}reloadResources(i,r,s){const f=Bl();return typeof i=="function"&&(s=i,i=void 0),typeof r=="function"&&(s=r,r=void 0),i||(i=this.languages),r||(r=this.options.ns),s||(s=ru),this.services.backendConnector.reload(i,r,o=>{f.resolve(),s(o)}),f}use(i){if(!i)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!i.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return i.type==="backend"&&(this.modules.backend=i),(i.type==="logger"||i.log&&i.warn&&i.error)&&(this.modules.logger=i),i.type==="languageDetector"&&(this.modules.languageDetector=i),i.type==="i18nFormat"&&(this.modules.i18nFormat=i),i.type==="postProcessor"&&Ih.addPostProcessor(i),i.type==="formatter"&&(this.modules.formatter=i),i.type==="3rdParty"&&this.modules.external.push(i),this}setResolvedLanguage(i){if(!(!i||!this.languages)&&!(["cimode","dev"].indexOf(i)>-1)){for(let r=0;r<this.languages.length;r++){const s=this.languages[r];if(!(["cimode","dev"].indexOf(s)>-1)&&this.store.hasLanguageSomeTranslations(s)){this.resolvedLanguage=s;break}}!this.resolvedLanguage&&this.languages.indexOf(i)<0&&this.store.hasLanguageSomeTranslations(i)&&(this.resolvedLanguage=i,this.languages.unshift(i))}}changeLanguage(i,r){this.isLanguageChangingTo=i;const s=Bl();this.emit("languageChanging",i);const f=p=>{this.language=p,this.languages=this.services.languageUtils.toResolveHierarchy(p),this.resolvedLanguage=void 0,this.setResolvedLanguage(p)},o=(p,y)=>{y?this.isLanguageChangingTo===i&&(f(y),this.translator.changeLanguage(y),this.isLanguageChangingTo=void 0,this.emit("languageChanged",y),this.logger.log("languageChanged",y)):this.isLanguageChangingTo=void 0,s.resolve((...m)=>this.t(...m)),r&&r(p,(...m)=>this.t(...m))},h=p=>{var v,E;!i&&!p&&this.services.languageDetector&&(p=[]);const y=te(p)?p:p&&p[0],m=this.store.hasLanguageSomeTranslations(y)?y:this.services.languageUtils.getBestMatchFromCodes(te(p)?[p]:p);m&&(this.language||f(m),this.translator.language||this.translator.changeLanguage(m),(E=(v=this.services.languageDetector)==null?void 0:v.cacheUserLanguage)==null||E.call(v,m)),this.loadResources(m,w=>{o(w,m)})};return!i&&this.services.languageDetector&&!this.services.languageDetector.async?h(this.services.languageDetector.detect()):!i&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(h):this.services.languageDetector.detect(h):h(i),s}getFixedT(i,r,s){const f=(o,h,...p)=>{let y;typeof h!="object"?y=this.options.overloadTranslationOptionHandler([o,h].concat(p)):y={...h},y.lng=y.lng||f.lng,y.lngs=y.lngs||f.lngs,y.ns=y.ns||f.ns,y.keyPrefix!==""&&(y.keyPrefix=y.keyPrefix||s||f.keyPrefix);const m=this.options.keySeparator||".";let v;return y.keyPrefix&&Array.isArray(o)?v=o.map(E=>`${y.keyPrefix}${m}${E}`):v=y.keyPrefix?`${y.keyPrefix}${m}${o}`:o,this.t(v,y)};return te(i)?f.lng=i:f.lngs=i,f.ns=r,f.keyPrefix=s,f}t(...i){var r;return(r=this.translator)==null?void 0:r.translate(...i)}exists(...i){var r;return(r=this.translator)==null?void 0:r.exists(...i)}setDefaultNamespace(i){this.options.defaultNS=i}hasLoadedNamespace(i,r={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const s=r.lng||this.resolvedLanguage||this.languages[0],f=this.options?this.options.fallbackLng:!1,o=this.languages[this.languages.length-1];if(s.toLowerCase()==="cimode")return!0;const h=(p,y)=>{const m=this.services.backendConnector.state[`${p}|${y}`];return m===-1||m===0||m===2};if(r.precheck){const p=r.precheck(this,h);if(p!==void 0)return p}return!!(this.hasResourceBundle(s,i)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||h(s,i)&&(!f||h(o,i)))}loadNamespaces(i,r){const s=Bl();return this.options.ns?(te(i)&&(i=[i]),i.forEach(f=>{this.options.ns.indexOf(f)<0&&this.options.ns.push(f)}),this.loadResources(f=>{s.resolve(),r&&r(f)}),s):(r&&r(),Promise.resolve())}loadLanguages(i,r){const s=Bl();te(i)&&(i=[i]);const f=this.options.preload||[],o=i.filter(h=>f.indexOf(h)<0&&this.services.languageUtils.isSupportedCode(h));return o.length?(this.options.preload=f.concat(o),this.loadResources(h=>{s.resolve(),r&&r(h)}),s):(r&&r(),Promise.resolve())}dir(i){var f,o;if(i||(i=this.resolvedLanguage||(((f=this.languages)==null?void 0:f.length)>0?this.languages[0]:this.language)),!i)return"rtl";const r=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],s=((o=this.services)==null?void 0:o.languageUtils)||new Ah(Dh());return r.indexOf(s.getLanguagePartFromCode(i))>-1||i.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(i={},r){return new kl(i,r)}cloneInstance(i={},r=ru){const s=i.forkResourceStore;s&&delete i.forkResourceStore;const f={...this.options,...i,isClone:!0},o=new kl(f);if((i.debug!==void 0||i.prefix!==void 0)&&(o.logger=o.logger.clone(i)),["store","services","language"].forEach(p=>{o[p]=this[p]}),o.services={...this.services},o.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},s){const p=Object.keys(this.store.data).reduce((y,m)=>(y[m]={...this.store.data[m]},y[m]=Object.keys(y[m]).reduce((v,E)=>(v[E]={...y[m][E]},v),y[m]),y),{});o.store=new Eh(p,f),o.services.resourceStore=o.store}return o.translator=new mu(o.services,f),o.translator.on("*",(p,...y)=>{o.emit(p,...y)}),o.init(f,r),o.translator.options=f,o.translator.backendConnector.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},o}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const Pe=kl.createInstance();Pe.createInstance=kl.createInstance;Pe.createInstance;Pe.dir;Pe.init;Pe.loadResources;Pe.reloadResources;Pe.use;Pe.changeLanguage;Pe.getFixedT;Pe.t;Pe.exists;Pe.setDefaultNamespace;Pe.hasLoadedNamespace;Pe.loadNamespaces;Pe.loadLanguages;const Vp="Network Device Dashboard",Gp={title:"Device Information",hostname:"Hostname",firmware:"Firmware",kernelTime:"Kernel Time",cpuId:"CPU ID",ethernetIp:"Ethernet IP",appVersion:"App Version",deviceTime:"Device Time"},Xp={title:"System Resources",cpuUsage:"CPU Usage",memory:"Memory",storage:"Storage",ethernetSpeed:"Ethernet Speed (Mbps)",wifiSpeed:"Wi-Fi Speed (Mbps)"},kp={title:"Wi-Fi Network Management",connected:"Connected",disconnect:"Disconnect",signalStrength:"Signal Strength",security:"Security",availableNetworks:"Available Networks",scan:"Scan",connect:"Connect",current:"Current",openNetwork:"Open Network",strong:"Strong",medium:"Medium",weak:"Weak",enterPassword:"Enter password",connectedStatus:"Connected",wpa3Blocked:"WPA3 networks are currently not supported"},Qp={refresh:"Refresh",scan:"Scan",connect:"Connect",disconnect:"Disconnect",cancel:"Cancel"},Zp={disconnectTitle:"Disconnect",disconnectMessage:'Are you sure you want to disconnect from "{{ssid}}"?'},Kp={connected:"Connected",disconnected:"Disconnected",connecting:"Connecting...",disconnecting:"Disconnecting..."},Jp={zh:"中文",en:"English",ja:"日本語",ko:"한국어"},$p={title:Vp,deviceInfo:Gp,systemResources:Xp,wifiManagement:kp,buttons:Qp,confirmDialog:Zp,status:Kp,languages:Jp},Fp="网络设备仪表板",Wp={title:"设备信息",hostname:"主机名",firmware:"固件版本",kernelTime:"内核时间",cpuId:"CPU ID",ethernetIp:"以太网IP",appVersion:"应用版本",deviceTime:"设备时间"},Pp={title:"系统资源",cpuUsage:"CPU 使用率",memory:"内存",storage:"存储",ethernetSpeed:"以太网速度 (Mbps)",wifiSpeed:"Wi-Fi 速度 (Mbps)"},Ip={title:"Wi-Fi 网络管理",connected:"已连接",disconnect:"断开连接",signalStrength:"信号强度",security:"安全性",availableNetworks:"可用网络",scan:"扫描",connect:"连接",current:"当前",openNetwork:"开放网络",strong:"强",medium:"中",weak:"弱",enterPassword:"请输入密码",connectedStatus:"已连接",wpa3Blocked:"WPA3 网络暂不支持连接"},ey={refresh:"刷新",scan:"扫描",connect:"连接",disconnect:"断开连接",cancel:"取消"},ty={disconnectTitle:"断开连接",disconnectMessage:'确定要断开与 "{{ssid}}" 的连接吗？'},ny={connected:"已连接",disconnected:"未连接",connecting:"连接中...",disconnecting:"断开中..."},ay={zh:"中文",en:"English",ja:"日本語",ko:"한국어"},ly={title:Fp,deviceInfo:Wp,systemResources:Pp,wifiManagement:Ip,buttons:ey,confirmDialog:ty,status:ny,languages:ay},iy="ネットワークデバイスダッシュボード",uy={title:"デバイス情報",hostname:"ホスト名",firmware:"ファームウェア",kernelTime:"カーネル時間",cpuId:"CPU ID",ethernetIp:"イーサネットIP",appVersion:"アプリバージョン",deviceTime:"デバイス時間"},sy={title:"システムリソース",cpuUsage:"CPU使用率",memory:"メモリ",storage:"ストレージ",ethernetSpeed:"イーサネット速度 (Mbps)",wifiSpeed:"Wi-Fi速度 (Mbps)"},cy={title:"Wi-Fiネットワーク管理",connected:"接続済み",disconnect:"切断",signalStrength:"信号強度",security:"セキュリティ",availableNetworks:"利用可能なネットワーク",scan:"スキャン",connect:"接続",current:"現在",openNetwork:"オープンネットワーク",strong:"強",medium:"中",weak:"弱",enterPassword:"パスワードを入力",connectedStatus:"接続済み",wpa3Blocked:"WPA3 ネットワークは現在サポートされていません"},ry={refresh:"更新",scan:"スキャン",connect:"接続",disconnect:"切断",cancel:"キャンセル"},fy={disconnectTitle:"切断",disconnectMessage:'"{{ssid}}" から切断してもよろしいですか？'},oy={connected:"接続済み",disconnected:"未接続",connecting:"接続中...",disconnecting:"切断中..."},dy={zh:"中文",en:"English",ja:"日本語",ko:"한국어"},hy={title:iy,deviceInfo:uy,systemResources:sy,wifiManagement:cy,buttons:ry,confirmDialog:fy,status:oy,languages:dy},gy="네트워크 장치 대시보드",my={title:"장치 정보",hostname:"호스트명",firmware:"펌웨어",kernelTime:"커널 시간",cpuId:"CPU ID",ethernetIp:"이더넷 IP",appVersion:"앱 버전",deviceTime:"장치 시간"},py={title:"시스템 리소스",cpuUsage:"CPU 사용률",memory:"메모리",storage:"저장소",ethernetSpeed:"이더넷 속도 (Mbps)",wifiSpeed:"Wi-Fi 속도 (Mbps)"},yy={title:"Wi-Fi 네트워크 관리",connected:"연결됨",disconnect:"연결 해제",signalStrength:"신호 강도",security:"보안",availableNetworks:"사용 가능한 네트워크",scan:"스캔",connect:"연결",current:"현재",openNetwork:"개방 네트워크",strong:"강함",medium:"보통",weak:"약함",enterPassword:"비밀번호 입력",connectedStatus:"연결됨",wpa3Blocked:"WPA3 네트워크는 현재 지원되지 않습니다"},vy={refresh:"새로고침",scan:"스캔",connect:"연결",disconnect:"연결 해제",cancel:"취소"},by={disconnectTitle:"연결 해제",disconnectMessage:'"{{ssid}}"에서 연결을 해제하시겠습니까？'},Sy={connected:"연결됨",disconnected:"연결 안됨",connecting:"연결 중...",disconnecting:"연결 해제 중..."},xy={zh:"中文",en:"English",ja:"日本語",ko:"한국어"},Ey={title:gy,deviceInfo:my,systemResources:py,wifiManagement:yy,buttons:vy,confirmDialog:by,status:Sy,languages:xy},Oy={en:{translation:$p},zh:{translation:ly},ja:{translation:hy},ko:{translation:Ey}};Pe.use(yp).init({resources:Oy,lng:"zh",fallbackLng:"en",interpolation:{escapeValue:!1}});/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ty=c=>c.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Ay=c=>c.replace(/^([A-Z])|[\s-_]+(\w)/g,(i,r,s)=>s?s.toUpperCase():r.toLowerCase()),wh=c=>{const i=Ay(c);return i.charAt(0).toUpperCase()+i.slice(1)},eg=(...c)=>c.filter((i,r,s)=>!!i&&i.trim()!==""&&s.indexOf(i)===r).join(" ").trim(),Ny=c=>{for(const i in c)if(i.startsWith("aria-")||i==="role"||i==="title")return!0};/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Ry={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const My=Se.forwardRef(({color:c="currentColor",size:i=24,strokeWidth:r=2,absoluteStrokeWidth:s,className:f="",children:o,iconNode:h,...p},y)=>Se.createElement("svg",{ref:y,...Ry,width:i,height:i,stroke:c,strokeWidth:s?Number(r)*24/Number(i):r,className:eg("lucide",f),...!o&&!Ny(p)&&{"aria-hidden":"true"},...p},[...h.map(([m,v])=>Se.createElement(m,v)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ge=(c,i)=>{const r=Se.forwardRef(({className:s,...f},o)=>Se.createElement(My,{ref:o,iconNode:i,className:eg(`lucide-${Ty(wh(c))}`,`lucide-${c}`,s),...f}));return r.displayName=wh(c),r};/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _y=[["path",{d:"M17 21v-2a1 1 0 0 1-1-1v-1a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1",key:"10bnsj"}],["path",{d:"M19 15V6.5a1 1 0 0 0-7 0v11a1 1 0 0 1-7 0V9",key:"1eqmu1"}],["path",{d:"M21 21v-2h-4",key:"14zm7j"}],["path",{d:"M3 5h4V3",key:"z442eg"}],["path",{d:"M7 5a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a1 1 0 0 1 1-1V3",key:"ebdjd7"}]],Dy=Ge("cable",_y);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zy=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],wy=Ge("calendar",zy);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uy=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],jy=Ge("chart-column",Uy);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cy=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]],Ly=Ge("clock",Cy);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hy=[["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M17 20v2",key:"1rnc9c"}],["path",{d:"M17 2v2",key:"11trls"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M2 17h2",key:"7oei6x"}],["path",{d:"M2 7h2",key:"asdhe0"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"M20 17h2",key:"1fpfkl"}],["path",{d:"M20 7h2",key:"1o8tra"}],["path",{d:"M7 20v2",key:"4gnj0m"}],["path",{d:"M7 2v2",key:"1i4yhu"}],["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"8",y:"8",width:"8",height:"8",rx:"1",key:"z9xiuo"}]],tg=Ge("cpu",Hy);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const By=[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],qy=Ge("eye-off",By);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yy=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Vy=Ge("eye",Yy);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gy=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],Xy=Ge("globe",Gy);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ky=[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]],Qy=Ge("hard-drive",ky);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zy=[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]],Ky=Ge("hash",Zy);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jy=[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 9.9-1",key:"1mm8w8"}]],$y=Ge("lock-open",Jy);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fy=[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]],Wy=Ge("lock",Fy);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Py=[["path",{d:"M6 19v-3",key:"1nvgqn"}],["path",{d:"M10 19v-3",key:"iu8nkm"}],["path",{d:"M14 19v-3",key:"kcehxu"}],["path",{d:"M18 19v-3",key:"1vh91z"}],["path",{d:"M8 11V9",key:"63erz4"}],["path",{d:"M16 11V9",key:"fru6f3"}],["path",{d:"M12 11V9",key:"ha00sb"}],["path",{d:"M2 15h20",key:"16ne18"}],["path",{d:"M2 7a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v1.1a2 2 0 0 0 0 3.837V17a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-5.1a2 2 0 0 0 0-3.837Z",key:"lhddv3"}]],Iy=Ge("memory-stick",Py);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const e1=[["rect",{x:"16",y:"16",width:"6",height:"6",rx:"1",key:"4q2zg0"}],["rect",{x:"2",y:"16",width:"6",height:"6",rx:"1",key:"8cvhb9"}],["rect",{x:"9",y:"2",width:"6",height:"6",rx:"1",key:"1egb70"}],["path",{d:"M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3",key:"1jsf9p"}],["path",{d:"M12 12V8",key:"2874zd"}]],t1=Ge("network",e1);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n1=[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]],a1=Ge("package",n1);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const l1=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],ng=Ge("refresh-cw",l1);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const i1=[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]],Uh=Ge("server",i1);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const u1=[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]],s1=Ge("triangle-alert",u1);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const c1=[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]],jh=Ge("wifi",c1);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const r1=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],f1=Ge("x",r1),o1=()=>{const{t:c,i18n:i}=vu(),r=f=>{i.changeLanguage(f)},s=[{code:"zh",label:c("languages.zh"),flag:"🇨🇳"},{code:"en",label:c("languages.en"),flag:"🇺🇸"},{code:"ja",label:c("languages.ja"),flag:"🇯🇵"},{code:"ko",label:c("languages.ko"),flag:"🇰🇷"}];return z.jsxs("div",{className:"max-w-7xl mx-auto mb-6 flex flex-col md:flex-row justify-between items-center gap-4",children:[z.jsxs("h1",{className:"text-3xl md:text-4xl font-bold text-blue-400 text-shadow flex items-center gap-3",children:[z.jsx(Xy,{className:"w-8 h-8"}),c("title")]}),z.jsx("div",{className:"flex items-center gap-2 bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-1 shadow-glass",children:s.map(f=>z.jsxs("button",{className:`
              relative px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 flex items-center gap-2
              ${i.language===f.code?"bg-blue-500 text-white shadow-lg shadow-blue-500/25 scale-105":"text-gray-300 hover:text-white hover:bg-white/10"}
            `,onClick:()=>r(f.code),children:[z.jsx("span",{className:"text-base",children:f.flag}),z.jsx("span",{children:f.label}),i.language===f.code&&z.jsx("div",{className:"absolute inset-0 bg-blue-500/20 rounded-lg animate-pulse"})]},f.code))})]})};function ag(c,i){return function(){return c.apply(i,arguments)}}const{toString:d1}=Object.prototype,{getPrototypeOf:rr}=Object,{iterator:Su,toStringTag:lg}=Symbol,xu=(c=>i=>{const r=d1.call(i);return c[r]||(c[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),Ut=c=>(c=c.toLowerCase(),i=>xu(i)===c),Eu=c=>i=>typeof i===c,{isArray:Ha}=Array,Ql=Eu("undefined");function h1(c){return c!==null&&!Ql(c)&&c.constructor!==null&&!Ql(c.constructor)&&rt(c.constructor.isBuffer)&&c.constructor.isBuffer(c)}const ig=Ut("ArrayBuffer");function g1(c){let i;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?i=ArrayBuffer.isView(c):i=c&&c.buffer&&ig(c.buffer),i}const m1=Eu("string"),rt=Eu("function"),ug=Eu("number"),Ou=c=>c!==null&&typeof c=="object",p1=c=>c===!0||c===!1,fu=c=>{if(xu(c)!=="object")return!1;const i=rr(c);return(i===null||i===Object.prototype||Object.getPrototypeOf(i)===null)&&!(lg in c)&&!(Su in c)},y1=Ut("Date"),v1=Ut("File"),b1=Ut("Blob"),S1=Ut("FileList"),x1=c=>Ou(c)&&rt(c.pipe),E1=c=>{let i;return c&&(typeof FormData=="function"&&c instanceof FormData||rt(c.append)&&((i=xu(c))==="formdata"||i==="object"&&rt(c.toString)&&c.toString()==="[object FormData]"))},O1=Ut("URLSearchParams"),[T1,A1,N1,R1]=["ReadableStream","Request","Response","Headers"].map(Ut),M1=c=>c.trim?c.trim():c.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Zl(c,i,{allOwnKeys:r=!1}={}){if(c===null||typeof c>"u")return;let s,f;if(typeof c!="object"&&(c=[c]),Ha(c))for(s=0,f=c.length;s<f;s++)i.call(null,c[s],s,c);else{const o=r?Object.getOwnPropertyNames(c):Object.keys(c),h=o.length;let p;for(s=0;s<h;s++)p=o[s],i.call(null,c[p],p,c)}}function sg(c,i){i=i.toLowerCase();const r=Object.keys(c);let s=r.length,f;for(;s-- >0;)if(f=r[s],i===f.toLowerCase())return f;return null}const Kn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,cg=c=>!Ql(c)&&c!==Kn;function nr(){const{caseless:c}=cg(this)&&this||{},i={},r=(s,f)=>{const o=c&&sg(i,f)||f;fu(i[o])&&fu(s)?i[o]=nr(i[o],s):fu(s)?i[o]=nr({},s):Ha(s)?i[o]=s.slice():i[o]=s};for(let s=0,f=arguments.length;s<f;s++)arguments[s]&&Zl(arguments[s],r);return i}const _1=(c,i,r,{allOwnKeys:s}={})=>(Zl(i,(f,o)=>{r&&rt(f)?c[o]=ag(f,r):c[o]=f},{allOwnKeys:s}),c),D1=c=>(c.charCodeAt(0)===65279&&(c=c.slice(1)),c),z1=(c,i,r,s)=>{c.prototype=Object.create(i.prototype,s),c.prototype.constructor=c,Object.defineProperty(c,"super",{value:i.prototype}),r&&Object.assign(c.prototype,r)},w1=(c,i,r,s)=>{let f,o,h;const p={};if(i=i||{},c==null)return i;do{for(f=Object.getOwnPropertyNames(c),o=f.length;o-- >0;)h=f[o],(!s||s(h,c,i))&&!p[h]&&(i[h]=c[h],p[h]=!0);c=r!==!1&&rr(c)}while(c&&(!r||r(c,i))&&c!==Object.prototype);return i},U1=(c,i,r)=>{c=String(c),(r===void 0||r>c.length)&&(r=c.length),r-=i.length;const s=c.indexOf(i,r);return s!==-1&&s===r},j1=c=>{if(!c)return null;if(Ha(c))return c;let i=c.length;if(!ug(i))return null;const r=new Array(i);for(;i-- >0;)r[i]=c[i];return r},C1=(c=>i=>c&&i instanceof c)(typeof Uint8Array<"u"&&rr(Uint8Array)),L1=(c,i)=>{const s=(c&&c[Su]).call(c);let f;for(;(f=s.next())&&!f.done;){const o=f.value;i.call(c,o[0],o[1])}},H1=(c,i)=>{let r;const s=[];for(;(r=c.exec(i))!==null;)s.push(r);return s},B1=Ut("HTMLFormElement"),q1=c=>c.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,s,f){return s.toUpperCase()+f}),Ch=(({hasOwnProperty:c})=>(i,r)=>c.call(i,r))(Object.prototype),Y1=Ut("RegExp"),rg=(c,i)=>{const r=Object.getOwnPropertyDescriptors(c),s={};Zl(r,(f,o)=>{let h;(h=i(f,o,c))!==!1&&(s[o]=h||f)}),Object.defineProperties(c,s)},V1=c=>{rg(c,(i,r)=>{if(rt(c)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const s=c[r];if(rt(s)){if(i.enumerable=!1,"writable"in i){i.writable=!1;return}i.set||(i.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},G1=(c,i)=>{const r={},s=f=>{f.forEach(o=>{r[o]=!0})};return Ha(c)?s(c):s(String(c).split(i)),r},X1=()=>{},k1=(c,i)=>c!=null&&Number.isFinite(c=+c)?c:i;function Q1(c){return!!(c&&rt(c.append)&&c[lg]==="FormData"&&c[Su])}const Z1=c=>{const i=new Array(10),r=(s,f)=>{if(Ou(s)){if(i.indexOf(s)>=0)return;if(!("toJSON"in s)){i[f]=s;const o=Ha(s)?[]:{};return Zl(s,(h,p)=>{const y=r(h,f+1);!Ql(y)&&(o[p]=y)}),i[f]=void 0,o}}return s};return r(c,0)},K1=Ut("AsyncFunction"),J1=c=>c&&(Ou(c)||rt(c))&&rt(c.then)&&rt(c.catch),fg=((c,i)=>c?setImmediate:i?((r,s)=>(Kn.addEventListener("message",({source:f,data:o})=>{f===Kn&&o===r&&s.length&&s.shift()()},!1),f=>{s.push(f),Kn.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",rt(Kn.postMessage)),$1=typeof queueMicrotask<"u"?queueMicrotask.bind(Kn):typeof process<"u"&&process.nextTick||fg,F1=c=>c!=null&&rt(c[Su]),D={isArray:Ha,isArrayBuffer:ig,isBuffer:h1,isFormData:E1,isArrayBufferView:g1,isString:m1,isNumber:ug,isBoolean:p1,isObject:Ou,isPlainObject:fu,isReadableStream:T1,isRequest:A1,isResponse:N1,isHeaders:R1,isUndefined:Ql,isDate:y1,isFile:v1,isBlob:b1,isRegExp:Y1,isFunction:rt,isStream:x1,isURLSearchParams:O1,isTypedArray:C1,isFileList:S1,forEach:Zl,merge:nr,extend:_1,trim:M1,stripBOM:D1,inherits:z1,toFlatObject:w1,kindOf:xu,kindOfTest:Ut,endsWith:U1,toArray:j1,forEachEntry:L1,matchAll:H1,isHTMLForm:B1,hasOwnProperty:Ch,hasOwnProp:Ch,reduceDescriptors:rg,freezeMethods:V1,toObjectSet:G1,toCamelCase:q1,noop:X1,toFiniteNumber:k1,findKey:sg,global:Kn,isContextDefined:cg,isSpecCompliantForm:Q1,toJSONObject:Z1,isAsyncFn:K1,isThenable:J1,setImmediate:fg,asap:$1,isIterable:F1};function ae(c,i,r,s,f){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=c,this.name="AxiosError",i&&(this.code=i),r&&(this.config=r),s&&(this.request=s),f&&(this.response=f,this.status=f.status?f.status:null)}D.inherits(ae,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:D.toJSONObject(this.config),code:this.code,status:this.status}}});const og=ae.prototype,dg={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(c=>{dg[c]={value:c}});Object.defineProperties(ae,dg);Object.defineProperty(og,"isAxiosError",{value:!0});ae.from=(c,i,r,s,f,o)=>{const h=Object.create(og);return D.toFlatObject(c,h,function(y){return y!==Error.prototype},p=>p!=="isAxiosError"),ae.call(h,c.message,i,r,s,f),h.cause=c,h.name=c.name,o&&Object.assign(h,o),h};const W1=null;function ar(c){return D.isPlainObject(c)||D.isArray(c)}function hg(c){return D.endsWith(c,"[]")?c.slice(0,-2):c}function Lh(c,i,r){return c?c.concat(i).map(function(f,o){return f=hg(f),!r&&o?"["+f+"]":f}).join(r?".":""):i}function P1(c){return D.isArray(c)&&!c.some(ar)}const I1=D.toFlatObject(D,{},null,function(i){return/^is[A-Z]/.test(i)});function Tu(c,i,r){if(!D.isObject(c))throw new TypeError("target must be an object");i=i||new FormData,r=D.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(q,B){return!D.isUndefined(B[q])});const s=r.metaTokens,f=r.visitor||v,o=r.dots,h=r.indexes,y=(r.Blob||typeof Blob<"u"&&Blob)&&D.isSpecCompliantForm(i);if(!D.isFunction(f))throw new TypeError("visitor must be a function");function m(M){if(M===null)return"";if(D.isDate(M))return M.toISOString();if(D.isBoolean(M))return M.toString();if(!y&&D.isBlob(M))throw new ae("Blob is not supported. Use a Buffer instead.");return D.isArrayBuffer(M)||D.isTypedArray(M)?y&&typeof Blob=="function"?new Blob([M]):Buffer.from(M):M}function v(M,q,B){let I=M;if(M&&!B&&typeof M=="object"){if(D.endsWith(q,"{}"))q=s?q:q.slice(0,-2),M=JSON.stringify(M);else if(D.isArray(M)&&P1(M)||(D.isFileList(M)||D.endsWith(q,"[]"))&&(I=D.toArray(M)))return q=hg(q),I.forEach(function(F,le){!(D.isUndefined(F)||F===null)&&i.append(h===!0?Lh([q],le,o):h===null?q:q+"[]",m(F))}),!1}return ar(M)?!0:(i.append(Lh(B,q,o),m(M)),!1)}const E=[],w=Object.assign(I1,{defaultVisitor:v,convertValue:m,isVisitable:ar});function H(M,q){if(!D.isUndefined(M)){if(E.indexOf(M)!==-1)throw Error("Circular reference detected in "+q.join("."));E.push(M),D.forEach(M,function(I,$){(!(D.isUndefined(I)||I===null)&&f.call(i,I,D.isString($)?$.trim():$,q,w))===!0&&H(I,q?q.concat($):[$])}),E.pop()}}if(!D.isObject(c))throw new TypeError("data must be an object");return H(c),i}function Hh(c){const i={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(c).replace(/[!'()~]|%20|%00/g,function(s){return i[s]})}function fr(c,i){this._pairs=[],c&&Tu(c,this,i)}const gg=fr.prototype;gg.append=function(i,r){this._pairs.push([i,r])};gg.toString=function(i){const r=i?function(s){return i.call(this,s,Hh)}:Hh;return this._pairs.map(function(f){return r(f[0])+"="+r(f[1])},"").join("&")};function ev(c){return encodeURIComponent(c).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function mg(c,i,r){if(!i)return c;const s=r&&r.encode||ev;D.isFunction(r)&&(r={serialize:r});const f=r&&r.serialize;let o;if(f?o=f(i,r):o=D.isURLSearchParams(i)?i.toString():new fr(i,r).toString(s),o){const h=c.indexOf("#");h!==-1&&(c=c.slice(0,h)),c+=(c.indexOf("?")===-1?"?":"&")+o}return c}class Bh{constructor(){this.handlers=[]}use(i,r,s){return this.handlers.push({fulfilled:i,rejected:r,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(i){this.handlers[i]&&(this.handlers[i]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(i){D.forEach(this.handlers,function(s){s!==null&&i(s)})}}const pg={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},tv=typeof URLSearchParams<"u"?URLSearchParams:fr,nv=typeof FormData<"u"?FormData:null,av=typeof Blob<"u"?Blob:null,lv={isBrowser:!0,classes:{URLSearchParams:tv,FormData:nv,Blob:av},protocols:["http","https","file","blob","url","data"]},or=typeof window<"u"&&typeof document<"u",lr=typeof navigator=="object"&&navigator||void 0,iv=or&&(!lr||["ReactNative","NativeScript","NS"].indexOf(lr.product)<0),uv=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",sv=or&&window.location.href||"http://localhost",cv=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:or,hasStandardBrowserEnv:iv,hasStandardBrowserWebWorkerEnv:uv,navigator:lr,origin:sv},Symbol.toStringTag,{value:"Module"})),nt={...cv,...lv};function rv(c,i){return Tu(c,new nt.classes.URLSearchParams,Object.assign({visitor:function(r,s,f,o){return nt.isNode&&D.isBuffer(r)?(this.append(s,r.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},i))}function fv(c){return D.matchAll(/\w+|\[(\w*)]/g,c).map(i=>i[0]==="[]"?"":i[1]||i[0])}function ov(c){const i={},r=Object.keys(c);let s;const f=r.length;let o;for(s=0;s<f;s++)o=r[s],i[o]=c[o];return i}function yg(c){function i(r,s,f,o){let h=r[o++];if(h==="__proto__")return!0;const p=Number.isFinite(+h),y=o>=r.length;return h=!h&&D.isArray(f)?f.length:h,y?(D.hasOwnProp(f,h)?f[h]=[f[h],s]:f[h]=s,!p):((!f[h]||!D.isObject(f[h]))&&(f[h]=[]),i(r,s,f[h],o)&&D.isArray(f[h])&&(f[h]=ov(f[h])),!p)}if(D.isFormData(c)&&D.isFunction(c.entries)){const r={};return D.forEachEntry(c,(s,f)=>{i(fv(s),f,r,0)}),r}return null}function dv(c,i,r){if(D.isString(c))try{return(i||JSON.parse)(c),D.trim(c)}catch(s){if(s.name!=="SyntaxError")throw s}return(r||JSON.stringify)(c)}const Kl={transitional:pg,adapter:["xhr","http","fetch"],transformRequest:[function(i,r){const s=r.getContentType()||"",f=s.indexOf("application/json")>-1,o=D.isObject(i);if(o&&D.isHTMLForm(i)&&(i=new FormData(i)),D.isFormData(i))return f?JSON.stringify(yg(i)):i;if(D.isArrayBuffer(i)||D.isBuffer(i)||D.isStream(i)||D.isFile(i)||D.isBlob(i)||D.isReadableStream(i))return i;if(D.isArrayBufferView(i))return i.buffer;if(D.isURLSearchParams(i))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),i.toString();let p;if(o){if(s.indexOf("application/x-www-form-urlencoded")>-1)return rv(i,this.formSerializer).toString();if((p=D.isFileList(i))||s.indexOf("multipart/form-data")>-1){const y=this.env&&this.env.FormData;return Tu(p?{"files[]":i}:i,y&&new y,this.formSerializer)}}return o||f?(r.setContentType("application/json",!1),dv(i)):i}],transformResponse:[function(i){const r=this.transitional||Kl.transitional,s=r&&r.forcedJSONParsing,f=this.responseType==="json";if(D.isResponse(i)||D.isReadableStream(i))return i;if(i&&D.isString(i)&&(s&&!this.responseType||f)){const h=!(r&&r.silentJSONParsing)&&f;try{return JSON.parse(i)}catch(p){if(h)throw p.name==="SyntaxError"?ae.from(p,ae.ERR_BAD_RESPONSE,this,null,this.response):p}}return i}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:nt.classes.FormData,Blob:nt.classes.Blob},validateStatus:function(i){return i>=200&&i<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};D.forEach(["delete","get","head","post","put","patch"],c=>{Kl.headers[c]={}});const hv=D.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),gv=c=>{const i={};let r,s,f;return c&&c.split(`
`).forEach(function(h){f=h.indexOf(":"),r=h.substring(0,f).trim().toLowerCase(),s=h.substring(f+1).trim(),!(!r||i[r]&&hv[r])&&(r==="set-cookie"?i[r]?i[r].push(s):i[r]=[s]:i[r]=i[r]?i[r]+", "+s:s)}),i},qh=Symbol("internals");function ql(c){return c&&String(c).trim().toLowerCase()}function ou(c){return c===!1||c==null?c:D.isArray(c)?c.map(ou):String(c)}function mv(c){const i=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=r.exec(c);)i[s[1]]=s[2];return i}const pv=c=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(c.trim());function $c(c,i,r,s,f){if(D.isFunction(s))return s.call(this,i,r);if(f&&(i=r),!!D.isString(i)){if(D.isString(s))return i.indexOf(s)!==-1;if(D.isRegExp(s))return s.test(i)}}function yv(c){return c.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(i,r,s)=>r.toUpperCase()+s)}function vv(c,i){const r=D.toCamelCase(" "+i);["get","set","has"].forEach(s=>{Object.defineProperty(c,s+r,{value:function(f,o,h){return this[s].call(this,i,f,o,h)},configurable:!0})})}let ft=class{constructor(i){i&&this.set(i)}set(i,r,s){const f=this;function o(p,y,m){const v=ql(y);if(!v)throw new Error("header name must be a non-empty string");const E=D.findKey(f,v);(!E||f[E]===void 0||m===!0||m===void 0&&f[E]!==!1)&&(f[E||y]=ou(p))}const h=(p,y)=>D.forEach(p,(m,v)=>o(m,v,y));if(D.isPlainObject(i)||i instanceof this.constructor)h(i,r);else if(D.isString(i)&&(i=i.trim())&&!pv(i))h(gv(i),r);else if(D.isObject(i)&&D.isIterable(i)){let p={},y,m;for(const v of i){if(!D.isArray(v))throw TypeError("Object iterator must return a key-value pair");p[m=v[0]]=(y=p[m])?D.isArray(y)?[...y,v[1]]:[y,v[1]]:v[1]}h(p,r)}else i!=null&&o(r,i,s);return this}get(i,r){if(i=ql(i),i){const s=D.findKey(this,i);if(s){const f=this[s];if(!r)return f;if(r===!0)return mv(f);if(D.isFunction(r))return r.call(this,f,s);if(D.isRegExp(r))return r.exec(f);throw new TypeError("parser must be boolean|regexp|function")}}}has(i,r){if(i=ql(i),i){const s=D.findKey(this,i);return!!(s&&this[s]!==void 0&&(!r||$c(this,this[s],s,r)))}return!1}delete(i,r){const s=this;let f=!1;function o(h){if(h=ql(h),h){const p=D.findKey(s,h);p&&(!r||$c(s,s[p],p,r))&&(delete s[p],f=!0)}}return D.isArray(i)?i.forEach(o):o(i),f}clear(i){const r=Object.keys(this);let s=r.length,f=!1;for(;s--;){const o=r[s];(!i||$c(this,this[o],o,i,!0))&&(delete this[o],f=!0)}return f}normalize(i){const r=this,s={};return D.forEach(this,(f,o)=>{const h=D.findKey(s,o);if(h){r[h]=ou(f),delete r[o];return}const p=i?yv(o):String(o).trim();p!==o&&delete r[o],r[p]=ou(f),s[p]=!0}),this}concat(...i){return this.constructor.concat(this,...i)}toJSON(i){const r=Object.create(null);return D.forEach(this,(s,f)=>{s!=null&&s!==!1&&(r[f]=i&&D.isArray(s)?s.join(", "):s)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([i,r])=>i+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(i){return i instanceof this?i:new this(i)}static concat(i,...r){const s=new this(i);return r.forEach(f=>s.set(f)),s}static accessor(i){const s=(this[qh]=this[qh]={accessors:{}}).accessors,f=this.prototype;function o(h){const p=ql(h);s[p]||(vv(f,h),s[p]=!0)}return D.isArray(i)?i.forEach(o):o(i),this}};ft.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);D.reduceDescriptors(ft.prototype,({value:c},i)=>{let r=i[0].toUpperCase()+i.slice(1);return{get:()=>c,set(s){this[r]=s}}});D.freezeMethods(ft);function Fc(c,i){const r=this||Kl,s=i||r,f=ft.from(s.headers);let o=s.data;return D.forEach(c,function(p){o=p.call(r,o,f.normalize(),i?i.status:void 0)}),f.normalize(),o}function vg(c){return!!(c&&c.__CANCEL__)}function Ba(c,i,r){ae.call(this,c??"canceled",ae.ERR_CANCELED,i,r),this.name="CanceledError"}D.inherits(Ba,ae,{__CANCEL__:!0});function bg(c,i,r){const s=r.config.validateStatus;!r.status||!s||s(r.status)?c(r):i(new ae("Request failed with status code "+r.status,[ae.ERR_BAD_REQUEST,ae.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function bv(c){const i=/^([-+\w]{1,25})(:?\/\/|:)/.exec(c);return i&&i[1]||""}function Sv(c,i){c=c||10;const r=new Array(c),s=new Array(c);let f=0,o=0,h;return i=i!==void 0?i:1e3,function(y){const m=Date.now(),v=s[o];h||(h=m),r[f]=y,s[f]=m;let E=o,w=0;for(;E!==f;)w+=r[E++],E=E%c;if(f=(f+1)%c,f===o&&(o=(o+1)%c),m-h<i)return;const H=v&&m-v;return H?Math.round(w*1e3/H):void 0}}function xv(c,i){let r=0,s=1e3/i,f,o;const h=(m,v=Date.now())=>{r=v,f=null,o&&(clearTimeout(o),o=null),c.apply(null,m)};return[(...m)=>{const v=Date.now(),E=v-r;E>=s?h(m,v):(f=m,o||(o=setTimeout(()=>{o=null,h(f)},s-E)))},()=>f&&h(f)]}const pu=(c,i,r=3)=>{let s=0;const f=Sv(50,250);return xv(o=>{const h=o.loaded,p=o.lengthComputable?o.total:void 0,y=h-s,m=f(y),v=h<=p;s=h;const E={loaded:h,total:p,progress:p?h/p:void 0,bytes:y,rate:m||void 0,estimated:m&&p&&v?(p-h)/m:void 0,event:o,lengthComputable:p!=null,[i?"download":"upload"]:!0};c(E)},r)},Yh=(c,i)=>{const r=c!=null;return[s=>i[0]({lengthComputable:r,total:c,loaded:s}),i[1]]},Vh=c=>(...i)=>D.asap(()=>c(...i)),Ev=nt.hasStandardBrowserEnv?((c,i)=>r=>(r=new URL(r,nt.origin),c.protocol===r.protocol&&c.host===r.host&&(i||c.port===r.port)))(new URL(nt.origin),nt.navigator&&/(msie|trident)/i.test(nt.navigator.userAgent)):()=>!0,Ov=nt.hasStandardBrowserEnv?{write(c,i,r,s,f,o){const h=[c+"="+encodeURIComponent(i)];D.isNumber(r)&&h.push("expires="+new Date(r).toGMTString()),D.isString(s)&&h.push("path="+s),D.isString(f)&&h.push("domain="+f),o===!0&&h.push("secure"),document.cookie=h.join("; ")},read(c){const i=document.cookie.match(new RegExp("(^|;\\s*)("+c+")=([^;]*)"));return i?decodeURIComponent(i[3]):null},remove(c){this.write(c,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Tv(c){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(c)}function Av(c,i){return i?c.replace(/\/?\/$/,"")+"/"+i.replace(/^\/+/,""):c}function Sg(c,i,r){let s=!Tv(i);return c&&(s||r==!1)?Av(c,i):i}const Gh=c=>c instanceof ft?{...c}:c;function Fn(c,i){i=i||{};const r={};function s(m,v,E,w){return D.isPlainObject(m)&&D.isPlainObject(v)?D.merge.call({caseless:w},m,v):D.isPlainObject(v)?D.merge({},v):D.isArray(v)?v.slice():v}function f(m,v,E,w){if(D.isUndefined(v)){if(!D.isUndefined(m))return s(void 0,m,E,w)}else return s(m,v,E,w)}function o(m,v){if(!D.isUndefined(v))return s(void 0,v)}function h(m,v){if(D.isUndefined(v)){if(!D.isUndefined(m))return s(void 0,m)}else return s(void 0,v)}function p(m,v,E){if(E in i)return s(m,v);if(E in c)return s(void 0,m)}const y={url:o,method:o,data:o,baseURL:h,transformRequest:h,transformResponse:h,paramsSerializer:h,timeout:h,timeoutMessage:h,withCredentials:h,withXSRFToken:h,adapter:h,responseType:h,xsrfCookieName:h,xsrfHeaderName:h,onUploadProgress:h,onDownloadProgress:h,decompress:h,maxContentLength:h,maxBodyLength:h,beforeRedirect:h,transport:h,httpAgent:h,httpsAgent:h,cancelToken:h,socketPath:h,responseEncoding:h,validateStatus:p,headers:(m,v,E)=>f(Gh(m),Gh(v),E,!0)};return D.forEach(Object.keys(Object.assign({},c,i)),function(v){const E=y[v]||f,w=E(c[v],i[v],v);D.isUndefined(w)&&E!==p||(r[v]=w)}),r}const xg=c=>{const i=Fn({},c);let{data:r,withXSRFToken:s,xsrfHeaderName:f,xsrfCookieName:o,headers:h,auth:p}=i;i.headers=h=ft.from(h),i.url=mg(Sg(i.baseURL,i.url,i.allowAbsoluteUrls),c.params,c.paramsSerializer),p&&h.set("Authorization","Basic "+btoa((p.username||"")+":"+(p.password?unescape(encodeURIComponent(p.password)):"")));let y;if(D.isFormData(r)){if(nt.hasStandardBrowserEnv||nt.hasStandardBrowserWebWorkerEnv)h.setContentType(void 0);else if((y=h.getContentType())!==!1){const[m,...v]=y?y.split(";").map(E=>E.trim()).filter(Boolean):[];h.setContentType([m||"multipart/form-data",...v].join("; "))}}if(nt.hasStandardBrowserEnv&&(s&&D.isFunction(s)&&(s=s(i)),s||s!==!1&&Ev(i.url))){const m=f&&o&&Ov.read(o);m&&h.set(f,m)}return i},Nv=typeof XMLHttpRequest<"u",Rv=Nv&&function(c){return new Promise(function(r,s){const f=xg(c);let o=f.data;const h=ft.from(f.headers).normalize();let{responseType:p,onUploadProgress:y,onDownloadProgress:m}=f,v,E,w,H,M;function q(){H&&H(),M&&M(),f.cancelToken&&f.cancelToken.unsubscribe(v),f.signal&&f.signal.removeEventListener("abort",v)}let B=new XMLHttpRequest;B.open(f.method.toUpperCase(),f.url,!0),B.timeout=f.timeout;function I(){if(!B)return;const F=ft.from("getAllResponseHeaders"in B&&B.getAllResponseHeaders()),k={data:!p||p==="text"||p==="json"?B.responseText:B.response,status:B.status,statusText:B.statusText,headers:F,config:c,request:B};bg(function(fe){r(fe),q()},function(fe){s(fe),q()},k),B=null}"onloadend"in B?B.onloadend=I:B.onreadystatechange=function(){!B||B.readyState!==4||B.status===0&&!(B.responseURL&&B.responseURL.indexOf("file:")===0)||setTimeout(I)},B.onabort=function(){B&&(s(new ae("Request aborted",ae.ECONNABORTED,c,B)),B=null)},B.onerror=function(){s(new ae("Network Error",ae.ERR_NETWORK,c,B)),B=null},B.ontimeout=function(){let le=f.timeout?"timeout of "+f.timeout+"ms exceeded":"timeout exceeded";const k=f.transitional||pg;f.timeoutErrorMessage&&(le=f.timeoutErrorMessage),s(new ae(le,k.clarifyTimeoutError?ae.ETIMEDOUT:ae.ECONNABORTED,c,B)),B=null},o===void 0&&h.setContentType(null),"setRequestHeader"in B&&D.forEach(h.toJSON(),function(le,k){B.setRequestHeader(k,le)}),D.isUndefined(f.withCredentials)||(B.withCredentials=!!f.withCredentials),p&&p!=="json"&&(B.responseType=f.responseType),m&&([w,M]=pu(m,!0),B.addEventListener("progress",w)),y&&B.upload&&([E,H]=pu(y),B.upload.addEventListener("progress",E),B.upload.addEventListener("loadend",H)),(f.cancelToken||f.signal)&&(v=F=>{B&&(s(!F||F.type?new Ba(null,c,B):F),B.abort(),B=null)},f.cancelToken&&f.cancelToken.subscribe(v),f.signal&&(f.signal.aborted?v():f.signal.addEventListener("abort",v)));const $=bv(f.url);if($&&nt.protocols.indexOf($)===-1){s(new ae("Unsupported protocol "+$+":",ae.ERR_BAD_REQUEST,c));return}B.send(o||null)})},Mv=(c,i)=>{const{length:r}=c=c?c.filter(Boolean):[];if(i||r){let s=new AbortController,f;const o=function(m){if(!f){f=!0,p();const v=m instanceof Error?m:this.reason;s.abort(v instanceof ae?v:new Ba(v instanceof Error?v.message:v))}};let h=i&&setTimeout(()=>{h=null,o(new ae(`timeout ${i} of ms exceeded`,ae.ETIMEDOUT))},i);const p=()=>{c&&(h&&clearTimeout(h),h=null,c.forEach(m=>{m.unsubscribe?m.unsubscribe(o):m.removeEventListener("abort",o)}),c=null)};c.forEach(m=>m.addEventListener("abort",o));const{signal:y}=s;return y.unsubscribe=()=>D.asap(p),y}},_v=function*(c,i){let r=c.byteLength;if(r<i){yield c;return}let s=0,f;for(;s<r;)f=s+i,yield c.slice(s,f),s=f},Dv=async function*(c,i){for await(const r of zv(c))yield*_v(r,i)},zv=async function*(c){if(c[Symbol.asyncIterator]){yield*c;return}const i=c.getReader();try{for(;;){const{done:r,value:s}=await i.read();if(r)break;yield s}}finally{await i.cancel()}},Xh=(c,i,r,s)=>{const f=Dv(c,i);let o=0,h,p=y=>{h||(h=!0,s&&s(y))};return new ReadableStream({async pull(y){try{const{done:m,value:v}=await f.next();if(m){p(),y.close();return}let E=v.byteLength;if(r){let w=o+=E;r(w)}y.enqueue(new Uint8Array(v))}catch(m){throw p(m),m}},cancel(y){return p(y),f.return()}},{highWaterMark:2})},Au=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Eg=Au&&typeof ReadableStream=="function",wv=Au&&(typeof TextEncoder=="function"?(c=>i=>c.encode(i))(new TextEncoder):async c=>new Uint8Array(await new Response(c).arrayBuffer())),Og=(c,...i)=>{try{return!!c(...i)}catch{return!1}},Uv=Eg&&Og(()=>{let c=!1;const i=new Request(nt.origin,{body:new ReadableStream,method:"POST",get duplex(){return c=!0,"half"}}).headers.has("Content-Type");return c&&!i}),kh=64*1024,ir=Eg&&Og(()=>D.isReadableStream(new Response("").body)),yu={stream:ir&&(c=>c.body)};Au&&(c=>{["text","arrayBuffer","blob","formData","stream"].forEach(i=>{!yu[i]&&(yu[i]=D.isFunction(c[i])?r=>r[i]():(r,s)=>{throw new ae(`Response type '${i}' is not supported`,ae.ERR_NOT_SUPPORT,s)})})})(new Response);const jv=async c=>{if(c==null)return 0;if(D.isBlob(c))return c.size;if(D.isSpecCompliantForm(c))return(await new Request(nt.origin,{method:"POST",body:c}).arrayBuffer()).byteLength;if(D.isArrayBufferView(c)||D.isArrayBuffer(c))return c.byteLength;if(D.isURLSearchParams(c)&&(c=c+""),D.isString(c))return(await wv(c)).byteLength},Cv=async(c,i)=>{const r=D.toFiniteNumber(c.getContentLength());return r??jv(i)},Lv=Au&&(async c=>{let{url:i,method:r,data:s,signal:f,cancelToken:o,timeout:h,onDownloadProgress:p,onUploadProgress:y,responseType:m,headers:v,withCredentials:E="same-origin",fetchOptions:w}=xg(c);m=m?(m+"").toLowerCase():"text";let H=Mv([f,o&&o.toAbortSignal()],h),M;const q=H&&H.unsubscribe&&(()=>{H.unsubscribe()});let B;try{if(y&&Uv&&r!=="get"&&r!=="head"&&(B=await Cv(v,s))!==0){let k=new Request(i,{method:"POST",body:s,duplex:"half"}),pe;if(D.isFormData(s)&&(pe=k.headers.get("content-type"))&&v.setContentType(pe),k.body){const[fe,ie]=Yh(B,pu(Vh(y)));s=Xh(k.body,kh,fe,ie)}}D.isString(E)||(E=E?"include":"omit");const I="credentials"in Request.prototype;M=new Request(i,{...w,signal:H,method:r.toUpperCase(),headers:v.normalize().toJSON(),body:s,duplex:"half",credentials:I?E:void 0});let $=await fetch(M,w);const F=ir&&(m==="stream"||m==="response");if(ir&&(p||F&&q)){const k={};["status","statusText","headers"].forEach(se=>{k[se]=$[se]});const pe=D.toFiniteNumber($.headers.get("content-length")),[fe,ie]=p&&Yh(pe,pu(Vh(p),!0))||[];$=new Response(Xh($.body,kh,fe,()=>{ie&&ie(),q&&q()}),k)}m=m||"text";let le=await yu[D.findKey(yu,m)||"text"]($,c);return!F&&q&&q(),await new Promise((k,pe)=>{bg(k,pe,{data:le,headers:ft.from($.headers),status:$.status,statusText:$.statusText,config:c,request:M})})}catch(I){throw q&&q(),I&&I.name==="TypeError"&&/Load failed|fetch/i.test(I.message)?Object.assign(new ae("Network Error",ae.ERR_NETWORK,c,M),{cause:I.cause||I}):ae.from(I,I&&I.code,c,M)}}),ur={http:W1,xhr:Rv,fetch:Lv};D.forEach(ur,(c,i)=>{if(c){try{Object.defineProperty(c,"name",{value:i})}catch{}Object.defineProperty(c,"adapterName",{value:i})}});const Qh=c=>`- ${c}`,Hv=c=>D.isFunction(c)||c===null||c===!1,Tg={getAdapter:c=>{c=D.isArray(c)?c:[c];const{length:i}=c;let r,s;const f={};for(let o=0;o<i;o++){r=c[o];let h;if(s=r,!Hv(r)&&(s=ur[(h=String(r)).toLowerCase()],s===void 0))throw new ae(`Unknown adapter '${h}'`);if(s)break;f[h||"#"+o]=s}if(!s){const o=Object.entries(f).map(([p,y])=>`adapter ${p} `+(y===!1?"is not supported by the environment":"is not available in the build"));let h=i?o.length>1?`since :
`+o.map(Qh).join(`
`):" "+Qh(o[0]):"as no adapter specified";throw new ae("There is no suitable adapter to dispatch the request "+h,"ERR_NOT_SUPPORT")}return s},adapters:ur};function Wc(c){if(c.cancelToken&&c.cancelToken.throwIfRequested(),c.signal&&c.signal.aborted)throw new Ba(null,c)}function Zh(c){return Wc(c),c.headers=ft.from(c.headers),c.data=Fc.call(c,c.transformRequest),["post","put","patch"].indexOf(c.method)!==-1&&c.headers.setContentType("application/x-www-form-urlencoded",!1),Tg.getAdapter(c.adapter||Kl.adapter)(c).then(function(s){return Wc(c),s.data=Fc.call(c,c.transformResponse,s),s.headers=ft.from(s.headers),s},function(s){return vg(s)||(Wc(c),s&&s.response&&(s.response.data=Fc.call(c,c.transformResponse,s.response),s.response.headers=ft.from(s.response.headers))),Promise.reject(s)})}const Ag="1.10.0",Nu={};["object","boolean","number","function","string","symbol"].forEach((c,i)=>{Nu[c]=function(s){return typeof s===c||"a"+(i<1?"n ":" ")+c}});const Kh={};Nu.transitional=function(i,r,s){function f(o,h){return"[Axios v"+Ag+"] Transitional option '"+o+"'"+h+(s?". "+s:"")}return(o,h,p)=>{if(i===!1)throw new ae(f(h," has been removed"+(r?" in "+r:"")),ae.ERR_DEPRECATED);return r&&!Kh[h]&&(Kh[h]=!0,console.warn(f(h," has been deprecated since v"+r+" and will be removed in the near future"))),i?i(o,h,p):!0}};Nu.spelling=function(i){return(r,s)=>(console.warn(`${s} is likely a misspelling of ${i}`),!0)};function Bv(c,i,r){if(typeof c!="object")throw new ae("options must be an object",ae.ERR_BAD_OPTION_VALUE);const s=Object.keys(c);let f=s.length;for(;f-- >0;){const o=s[f],h=i[o];if(h){const p=c[o],y=p===void 0||h(p,o,c);if(y!==!0)throw new ae("option "+o+" must be "+y,ae.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new ae("Unknown option "+o,ae.ERR_BAD_OPTION)}}const du={assertOptions:Bv,validators:Nu},Yt=du.validators;let $n=class{constructor(i){this.defaults=i||{},this.interceptors={request:new Bh,response:new Bh}}async request(i,r){try{return await this._request(i,r)}catch(s){if(s instanceof Error){let f={};Error.captureStackTrace?Error.captureStackTrace(f):f=new Error;const o=f.stack?f.stack.replace(/^.+\n/,""):"";try{s.stack?o&&!String(s.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+o):s.stack=o}catch{}}throw s}}_request(i,r){typeof i=="string"?(r=r||{},r.url=i):r=i||{},r=Fn(this.defaults,r);const{transitional:s,paramsSerializer:f,headers:o}=r;s!==void 0&&du.assertOptions(s,{silentJSONParsing:Yt.transitional(Yt.boolean),forcedJSONParsing:Yt.transitional(Yt.boolean),clarifyTimeoutError:Yt.transitional(Yt.boolean)},!1),f!=null&&(D.isFunction(f)?r.paramsSerializer={serialize:f}:du.assertOptions(f,{encode:Yt.function,serialize:Yt.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),du.assertOptions(r,{baseUrl:Yt.spelling("baseURL"),withXsrfToken:Yt.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let h=o&&D.merge(o.common,o[r.method]);o&&D.forEach(["delete","get","head","post","put","patch","common"],M=>{delete o[M]}),r.headers=ft.concat(h,o);const p=[];let y=!0;this.interceptors.request.forEach(function(q){typeof q.runWhen=="function"&&q.runWhen(r)===!1||(y=y&&q.synchronous,p.unshift(q.fulfilled,q.rejected))});const m=[];this.interceptors.response.forEach(function(q){m.push(q.fulfilled,q.rejected)});let v,E=0,w;if(!y){const M=[Zh.bind(this),void 0];for(M.unshift.apply(M,p),M.push.apply(M,m),w=M.length,v=Promise.resolve(r);E<w;)v=v.then(M[E++],M[E++]);return v}w=p.length;let H=r;for(E=0;E<w;){const M=p[E++],q=p[E++];try{H=M(H)}catch(B){q.call(this,B);break}}try{v=Zh.call(this,H)}catch(M){return Promise.reject(M)}for(E=0,w=m.length;E<w;)v=v.then(m[E++],m[E++]);return v}getUri(i){i=Fn(this.defaults,i);const r=Sg(i.baseURL,i.url,i.allowAbsoluteUrls);return mg(r,i.params,i.paramsSerializer)}};D.forEach(["delete","get","head","options"],function(i){$n.prototype[i]=function(r,s){return this.request(Fn(s||{},{method:i,url:r,data:(s||{}).data}))}});D.forEach(["post","put","patch"],function(i){function r(s){return function(o,h,p){return this.request(Fn(p||{},{method:i,headers:s?{"Content-Type":"multipart/form-data"}:{},url:o,data:h}))}}$n.prototype[i]=r(),$n.prototype[i+"Form"]=r(!0)});let qv=class Ng{constructor(i){if(typeof i!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(o){r=o});const s=this;this.promise.then(f=>{if(!s._listeners)return;let o=s._listeners.length;for(;o-- >0;)s._listeners[o](f);s._listeners=null}),this.promise.then=f=>{let o;const h=new Promise(p=>{s.subscribe(p),o=p}).then(f);return h.cancel=function(){s.unsubscribe(o)},h},i(function(o,h,p){s.reason||(s.reason=new Ba(o,h,p),r(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(i){if(this.reason){i(this.reason);return}this._listeners?this._listeners.push(i):this._listeners=[i]}unsubscribe(i){if(!this._listeners)return;const r=this._listeners.indexOf(i);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const i=new AbortController,r=s=>{i.abort(s)};return this.subscribe(r),i.signal.unsubscribe=()=>this.unsubscribe(r),i.signal}static source(){let i;return{token:new Ng(function(f){i=f}),cancel:i}}};function Yv(c){return function(r){return c.apply(null,r)}}function Vv(c){return D.isObject(c)&&c.isAxiosError===!0}const sr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(sr).forEach(([c,i])=>{sr[i]=c});function Rg(c){const i=new $n(c),r=ag($n.prototype.request,i);return D.extend(r,$n.prototype,i,{allOwnKeys:!0}),D.extend(r,i,null,{allOwnKeys:!0}),r.create=function(f){return Rg(Fn(c,f))},r}const He=Rg(Kl);He.Axios=$n;He.CanceledError=Ba;He.CancelToken=qv;He.isCancel=vg;He.VERSION=Ag;He.toFormData=Tu;He.AxiosError=ae;He.Cancel=He.CanceledError;He.all=function(i){return Promise.all(i)};He.spread=Yv;He.isAxiosError=Vv;He.mergeConfig=Fn;He.AxiosHeaders=ft;He.formToJSON=c=>yg(D.isHTMLForm(c)?new FormData(c):c);He.getAdapter=Tg.getAdapter;He.HttpStatusCode=sr;He.default=He;const{Axios:Fv,AxiosError:Wv,CanceledError:Pv,isCancel:Iv,CancelToken:eb,VERSION:tb,all:nb,Cancel:ab,isAxiosError:lb,spread:ib,toFormData:ub,AxiosHeaders:sb,HttpStatusCode:cb,formToJSON:rb,getAdapter:fb,mergeConfig:ob}=He,Gv="/api",Yl=He.create({baseURL:Gv,timeout:1e4}),Vl={async getDeviceInfo(){try{return(await Yl.get("/device/info")).data}catch(c){return console.warn("API call failed, using mock data:",c),{code:0,data:{app_version:"",cpu_id:"",cpu_usage:"",device_time:new Date().toISOString(),disk_total:"",disk_usage:"",eth0_Speed:"",eth0_ip:"",firmware:"",hostname:"",kernel_time:new Date().toISOString(),memory_total:"",memory_usage:"",otg_mode:""},msg:""}}},async getWiFiList(){try{return(await Yl.get("/wifi/list")).data}catch(c){return console.warn("API call failed, using mock data:",c),{code:0,data:[{bssid:"",channel:"",is_secure:!0,mode:"",rate:"",security:"",signal:0,ssid:""}],msg:""}}},async getWiFiStatus(){try{return(await Yl.get("/wifi/status")).data}catch(c){return console.warn("API call failed, using mock data:",c),{code:0,data:{DNS:[""],GATEWAY:"",IP4:"",device:"",ssid:"",state:"disconnected"},msg:""}}},async connectWiFi(c,i){try{return(await Yl.get(`/wifi/connect/${c}`,{params:{password:i}})).data}catch(r){return console.warn("API call failed, using mock response:",r),{code:0,data:{success:!0},msg:"Connected successfully"}}},async disconnectWiFi(){try{return(await Yl.get("/wifi/disconnect")).data}catch(c){return console.warn("API call failed, using mock response:",c),{code:0,data:{success:!0},msg:"Disconnected successfully"}}}},Xv=({deviceInfo:c,loading:i,onRefresh:r})=>{const{t:s}=vu(),f=()=>{r()},o=[{icon:Uh,label:s("deviceInfo.hostname"),value:c==null?void 0:c.hostname,color:"text-blue-400"},{icon:a1,label:s("deviceInfo.firmware"),value:c==null?void 0:c.firmware,color:"text-green-400"},{icon:Ly,label:s("deviceInfo.kernelTime"),value:c==null?void 0:c.kernel_time,color:"text-purple-400"},{icon:tg,label:s("deviceInfo.cpuId"),value:c==null?void 0:c.cpu_id,color:"text-orange-400"},{icon:t1,label:s("deviceInfo.ethernetIp"),value:c==null?void 0:c.eth0_ip,color:"text-cyan-400"},{icon:Ky,label:s("deviceInfo.appVersion"),value:c==null?void 0:c.app_version,color:"text-pink-400"},{icon:wy,label:s("deviceInfo.deviceTime"),value:c==null?void 0:c.device_time,color:"text-yellow-400"}];return z.jsxs("div",{className:"glass-card p-6 flex-1",children:[z.jsxs("div",{className:"flex items-center justify-between mb-5",children:[z.jsxs("h2",{className:"text-xl font-semibold text-blue-400 flex items-center gap-3",children:[z.jsx(Uh,{size:24,className:"text-blue-400"}),s("deviceInfo.title")]}),z.jsx("button",{className:`
            w-9 h-9 bg-primary-500 hover:bg-primary-600 rounded-lg flex items-center justify-center
            transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed
            ${i?"animate-spin":"hover:rotate-180"}
          `,onClick:f,disabled:i,children:z.jsx(ng,{size:18,className:"text-white"})})]}),z.jsx("div",{className:"grid grid-cols-1 gap-4",children:o.map((h,p)=>{const y=h.icon;return z.jsxs("div",{className:"flex items-center gap-3 p-3 bg-white/3 rounded-lg border border-white/5 hover:bg-white/5 transition-all duration-300",children:[z.jsx(y,{size:20,className:`${h.color} flex-shrink-0`}),z.jsx("span",{className:"text-gray-300 text-sm min-w-[100px] flex-shrink-0",children:h.label}),z.jsx("span",{className:"text-white font-medium text-sm ml-auto text-right break-all",children:i?z.jsxs("div",{className:"flex items-center gap-2",children:[z.jsx("div",{className:"loading"}),z.jsx("span",{children:"..."})]}):h.value||"N/A"})]},p)})})]})},kv=({deviceInfo:c,loading:i})=>{const{t:r}=vu(),s=Se.useMemo(()=>{if(!c)return{cpuUsage:0,memoryUsage:0,memoryUsed:"0MB",memoryTotal:"0MB",diskUsage:0,diskUsed:"0GB",diskTotal:"0GB",ethSpeed:"0",wifiSpeed:"-"};const o=parseFloat(c.cpu_usage)||0,h=c.memory_usage.match(/([0-9.]+)G\/([0-9.]+)G, ([0-9.]+)%/),p=h?parseFloat(h[3]):0,y=h?`${h[1]}GB`:"0GB",m=h?`${h[2]}GB`:"0GB",v=c.disk_usage.match(/([0-9.]+)G\/([0-9.]+)G, ([0-9.]+)%/),E=v?parseFloat(v[3]):0,w=v?`${v[1]}GB`:"0GB",H=v?`${v[2]}GB`:"0GB",M=c.eth0_Speed.match(/([0-9]+)/),q=M?M[1]:"0";return{cpuUsage:o,memoryUsage:p,memoryUsed:y,memoryTotal:m,diskUsage:E,diskUsed:w,diskTotal:H,ethSpeed:q,wifiSpeed:"-"}},[c]),f=({percentage:o,label:h,value:p,icon:y,color:m})=>{const E=2*Math.PI*35,w=E,H=E-o/100*E;return z.jsxs("div",{className:"text-center",children:[z.jsxs("div",{className:"relative w-20 h-20 mx-auto mb-3",children:[z.jsxs("svg",{width:"80",height:"80",className:"absolute inset-0 transform -rotate-90",children:[z.jsx("circle",{cx:"40",cy:"40",r:35,fill:"transparent",stroke:"rgba(64, 192, 255, 0.2)",strokeWidth:"3"}),z.jsx("circle",{cx:"40",cy:"40",r:35,fill:"transparent",stroke:m,strokeWidth:"3",strokeDasharray:w,strokeDashoffset:H,strokeLinecap:"round",className:"transition-all duration-500 ease-out"})]}),z.jsxs("div",{className:"absolute inset-0 flex flex-col items-center justify-center",children:[z.jsx(y,{size:16,className:`${m.replace("#","text-")} mb-1`}),z.jsxs("span",{className:"text-sm font-semibold text-blue-400",children:[Math.round(o),"%"]})]})]}),z.jsxs("div",{className:"text-xs text-gray-300 leading-tight",children:[h,p&&z.jsxs(z.Fragment,{children:[z.jsx("br",{}),z.jsx("span",{className:"text-gray-400",children:p})]})]})]})};return z.jsxs("div",{className:"glass-card p-6 flex-1",children:[z.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[z.jsx(jy,{size:24,className:"text-blue-400"}),z.jsx("h2",{className:"text-xl font-semibold text-blue-400",children:r("systemResources.title")})]}),z.jsxs("div",{className:"grid grid-cols-3 gap-5 mb-6",children:[z.jsx(f,{percentage:i?0:s.cpuUsage,label:r("systemResources.cpuUsage"),value:"",icon:tg,color:"#40c0ff"}),z.jsx(f,{percentage:i?0:s.memoryUsage,label:r("systemResources.memory"),value:i?"":`${s.memoryUsed}/${s.memoryTotal}`,icon:Iy,color:"#00ff88"}),z.jsx(f,{percentage:i?0:s.diskUsage,label:r("systemResources.storage"),value:i?"":`${s.diskUsed}/${s.diskTotal}`,icon:Qy,color:"#8058b0"})]}),z.jsx("div",{className:"grid grid-cols-1 md:grid-cols-1 gap-4",children:z.jsxs("div",{className:"bg-green-500/10 border border-green-500/30 rounded-xl p-4 text-center",children:[z.jsxs("div",{className:"flex items-center justify-center gap-2 mb-2",children:[z.jsx(Dy,{size:20,className:"text-green-400"}),z.jsx("span",{className:"text-green-400 text-sm font-medium",children:r("systemResources.ethernetSpeed")})]}),z.jsx("div",{className:"text-2xl font-bold text-green-500 mb-1",children:i?z.jsx("div",{className:"loading mx-auto"}):s.ethSpeed}),z.jsx("div",{className:"text-xs text-gray-400",children:"Mbps"})]})})]})},Qv=({networks:c,status:i,onScan:r,onConnect:s,onDisconnect:f})=>{const{t:o}=vu(),[h,p]=Se.useState(!1),[y,m]=Se.useState(null),[v,E]=Se.useState(!1),[w,H]=Se.useState(!1),[M,q]=Se.useState({ssid:"",show:!1}),[B,I]=Se.useState(""),[$,F]=Se.useState(!1),[le,k]=Se.useState(!1),pe=async()=>{p(!0),await r(),setTimeout(()=>p(!1),1e3)},fe=async(K,Me,_)=>{if(_&&_.includes("WPA3")){k(!0);return}if(k(!1),Me)q({ssid:K,show:!0}),I("");else{m(K);try{await s(K)}finally{m(null)}}},ie=async()=>{m(M.ssid),q({ssid:"",show:!1});try{await s(M.ssid,B)}finally{m(null),I("")}},se=()=>{H(!0)},ge=async()=>{H(!1),E(!0);try{await f()}finally{E(!1)}},ze=()=>{H(!1)},Be=K=>K>=70?"strong":K>=50?"medium":"weak",qe=({strength:K})=>{const Me=[25,50,75,100],_=Y=>K==="weak"?Y===0?1:.3:K==="medium"?Y<=1?1:.3:1;return z.jsx("div",{className:"flex items-end gap-0.5 w-4 h-4",children:Me.map((Y,Q)=>z.jsx("div",{className:"w-0.5 bg-blue-500 rounded-sm transition-all duration-300",style:{height:`${Y}%`,opacity:_(Q)}},Q))})};return z.jsxs("div",{className:"glass-card p-6 flex-[2]",children:[z.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[z.jsx(jh,{size:24,className:"text-blue-400"}),z.jsx("h2",{className:"text-xl font-semibold text-blue-400",children:o("wifiManagement.title")})]}),(i==null?void 0:i.state)==="connected"&&z.jsxs("div",{className:"relative bg-green-500/10 border border-green-500/30 rounded-xl p-5 mb-6 overflow-hidden",children:[z.jsx("div",{className:"absolute top-0 left-0 w-1 h-full bg-green-500"}),z.jsxs("div",{className:"flex items-center justify-between mb-4",children:[z.jsxs("div",{className:"flex items-center gap-2",children:[z.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse-dot"}),z.jsx("span",{className:"text-green-400 font-semibold text-sm",children:o("wifiManagement.connected")})]}),z.jsx("button",{className:"btn-danger",onClick:se,disabled:v,children:v?z.jsx("div",{className:"loading"}):o("wifiManagement.disconnect")})]}),z.jsxs("div",{className:"flex items-center gap-4",children:[z.jsx("div",{className:"w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center",children:z.jsx(jh,{size:24,className:"text-white"})}),z.jsxs("div",{className:"flex-1",children:[z.jsx("h3",{className:"text-lg font-semibold text-white mb-1",children:i.ssid}),z.jsxs("p",{className:"text-gray-300 text-sm mb-1",children:[i.IP4,i.GATEWAY,i.DNS]}),z.jsxs("p",{className:"text-gray-400 text-xs",children:[o("wifiManagement.signalStrength"),": ",o("wifiManagement.strong")]})]})]})]}),z.jsxs("div",{className:"flex items-center justify-between gap-3 mb-4 pb-3 border-b border-white/10",children:[z.jsx("span",{className:"text-white font-semibold",children:o("wifiManagement.availableNetworks")}),z.jsxs("button",{className:"btn-secondary flex items-center gap-2 text-xs",onClick:pe,disabled:h,children:[z.jsx(ng,{size:14,className:h?"animate-spin":""}),o("wifiManagement.scan")]})]}),z.jsx("div",{className:"max-h-80 overflow-y-auto custom-scrollbar space-y-2 pt-5",children:c.map((K,Me)=>z.jsxs("div",{className:`
              flex items-center justify-between p-4 rounded-lg border transition-all duration-300 cursor-pointer relative
              ${(i==null?void 0:i.ssid)===K.ssid?"bg-green-500/10 border-green-500/30 cursor-default shadow-lg shadow-green-500/20":"bg-white/5 border-white/10 hover:bg-blue-500/10 hover:border-blue-500/30 hover:-translate-y-0.5"}
            `,onClick:()=>(i==null?void 0:i.ssid)!==K.ssid&&fe(K.ssid,K.is_secure,K.security||void 0),children:[(i==null?void 0:i.ssid)===K.ssid&&z.jsx("div",{className:"absolute left-0 top-0 bottom-0 w-1 bg-green-500 rounded-l-lg"}),z.jsxs("div",{className:"flex items-center gap-3 flex-1",children:[z.jsx(qe,{strength:Be(K.signal)}),z.jsxs("div",{className:"flex-1",children:[z.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[z.jsx("span",{className:"text-white font-medium text-sm",children:K.ssid}),(i==null?void 0:i.ssid)===K.ssid&&z.jsx("span",{className:"bg-green-500 text-black text-xs font-semibold px-2 py-0.5 rounded",children:o("wifiManagement.current")})]}),z.jsx("div",{className:"text-gray-400 text-xs",children:K.security||o("wifiManagement.openNetwork")})]})]}),z.jsxs("div",{className:"flex items-center gap-2",children:[K.is_secure?z.jsx(Wy,{className:"text-gray-400",size:16}):z.jsx($y,{className:"text-gray-400",size:16}),(i==null?void 0:i.ssid)===K.ssid?z.jsxs("div",{className:"flex items-center gap-2 px-3 py-1 rounded-lg bg-green-100 border border-green-300",style:{background:"rgba(34, 197, 94, 0.2)",border:"1px solid rgba(34, 197, 94, 0.4)"},children:[z.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),z.jsx("span",{className:"text-green-400 text-xs font-medium",children:o("wifiManagement.connectedStatus")})]}):z.jsx("button",{className:"btn-primary text-xs px-3 py-1",disabled:y===K.ssid,onClick:_=>{_.stopPropagation(),fe(K.ssid,K.is_secure,K.security||void 0)},children:y===K.ssid?z.jsx("div",{className:"loading"}):o("wifiManagement.connect")})]})]},`${K.bssid}-${Me}`))}),le&&z.jsx("div",{className:"mt-4 text-center text-red-400 font-semibold text-sm",children:o("wifiManagement.wpa3Blocked")||"WPA3 网络暂不支持连接"}),w&&z.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50",children:z.jsxs("div",{className:"bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-6 max-w-md mx-4 shadow-2xl",children:[z.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[z.jsx("div",{className:"w-12 h-12 bg-red-500/20 rounded-full flex items-center justify-center",children:z.jsx(s1,{size:24,className:"text-red-400"})}),z.jsxs("div",{children:[z.jsx("h3",{className:"text-lg font-semibold text-white mb-1",children:o("wifiManagement.disconnect")}),z.jsx("p",{className:"text-gray-300 text-sm",children:o("confirmDialog.disconnectMessage",{ssid:i==null?void 0:i.ssid})})]})]}),z.jsxs("div",{className:"flex gap-3 justify-end",children:[z.jsx("button",{className:"btn-secondary",onClick:ze,children:o("buttons.cancel")||"Cancel"}),z.jsx("button",{className:"bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300",onClick:ge,children:o("wifiManagement.disconnect")})]})]})}),M.show&&z.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50",children:z.jsxs("div",{className:"bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-6 max-w-md mx-4 shadow-2xl",children:[z.jsxs("div",{className:"flex items-center justify-between mb-4",children:[z.jsxs("h3",{className:"text-lg font-semibold text-blue-400",children:[o("wifiManagement.connect")," ",M.ssid]}),z.jsx("button",{onClick:()=>q({ssid:"",show:!1}),className:"text-gray-400 hover:text-white transition-colors",children:z.jsx(f1,{size:20})})]}),z.jsxs("div",{className:"relative mb-4",children:[z.jsx("input",{type:$?"text":"password",placeholder:o("wifiManagement.enterPassword")||"Enter password",value:B,onChange:K=>I(K.target.value),onKeyDown:K=>K.key==="Enter"&&B.length>7&&ie(),className:"w-full p-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500 pr-12",autoFocus:!0}),z.jsx("button",{type:"button",className:"absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white",tabIndex:-1,onClick:()=>F(K=>!K),children:$?z.jsx(qy,{size:20}):z.jsx(Vy,{size:20})})]}),z.jsxs("div",{className:"flex gap-3 justify-end",children:[z.jsx("button",{className:"btn-secondary",onClick:()=>q({ssid:"",show:!1}),children:o("buttons.cancel")||"Cancel"}),z.jsx("button",{className:"btn-primary",onClick:ie,disabled:!B,children:o("wifiManagement.connect")})]})]})})]})},Zv=()=>{const[c,i]=Se.useState(null),[r,s]=Se.useState([]),[f,o]=Se.useState(null),[h,p]=Se.useState(!0),y=async()=>{try{const w=await Vl.getDeviceInfo();w.code===0&&i(w.data)}catch(w){console.error("Failed to fetch device info:",w)}},m=async()=>{try{const w=await Vl.getWiFiList();w.code===0&&s(w.data)}catch(w){console.error("Failed to fetch WiFi list:",w)}},v=async()=>{try{const w=await Vl.getWiFiStatus();w.code===0&&o(w.data)}catch(w){console.error("Failed to fetch WiFi status:",w)}},E=async()=>{p(!0),await Promise.all([y(),m(),v()]),p(!1)};return Se.useEffect(()=>{E();const w=setInterval(()=>{y(),v()},5e3);return()=>clearInterval(w)},[]),z.jsxs("div",{className:"max-w-7xl mx-auto",children:[z.jsx(o1,{}),z.jsxs("div",{className:"grid grid-cols-1 xl:grid-cols-5 gap-6 min-h-[calc(100vh-120px)]",children:[z.jsxs("div",{className:"xl:col-span-2 flex flex-col gap-6",children:[z.jsx(Xv,{deviceInfo:c,loading:h,onRefresh:y}),z.jsx(kv,{deviceInfo:c,loading:h})]}),z.jsx("div",{className:"xl:col-span-3 flex flex-col",children:z.jsx(Qv,{networks:r,status:f,loading:h,onScan:m,onConnect:async(w,H)=>{await Vl.connectWiFi(w,H),await v()},onDisconnect:async()=>{await Vl.disconnectWiFi(),await v()}})})]})]})};function Kv(){return z.jsx(xp,{i18n:Pe,children:z.jsx("div",{className:"min-h-screen p-5",children:z.jsx(Zv,{})})})}ip.createRoot(document.getElementById("root")).render(z.jsx(Se.StrictMode,{children:z.jsx(Kv,{})}));
